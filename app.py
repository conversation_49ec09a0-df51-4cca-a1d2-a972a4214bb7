"""
<PERSON>ğ Topolojisi Yönetim Sistemi
Ana uygulama dosyası
"""
import os
from flask import Flask
from flask_login import LoginManager

# Konfigürasyon ve modelleri import et
from config import config
from models import db, User
from routes import register_routes

def create_app(config_name=None):
    """Flask uygulaması oluştur ve yapılandır"""
    
    # Flask uygulaması oluştur
    app = Flask(__name__)
    
    # Konfigürasyon yükle
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(config[config_name])
    
    # Upload klasörünü oluştur
    upload_folder = app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    
    # Veritabanını başlat
    db.init_app(app)
    
    # Login manager'ı başlat
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Bu sayfaya erişmek için giriş yapmalısınız.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        """Kullanıcı yükleyici"""
        return User.query.get(int(user_id))
    
    # Route'ları kaydet
    register_routes(app)
    
    # Veritabanı tablolarını oluştur
    with app.app_context():
        db.create_all()
    
    return app

def main():
    """Ana fonksiyon"""
    app = create_app()
    
    # Geliştirme sunucusunu başlat
    app.run(
        host='0.0.0.0',
        port=3000,
        debug=True
    )

if __name__ == '__main__':
    main()
