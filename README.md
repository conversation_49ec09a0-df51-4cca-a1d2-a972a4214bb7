# Ağ Topolojisi Yönetim Sistemi

Modern ve kullanıcı dostu bir ağ topolojisi yönetim sistemi. Flask tabanlı web uygulaması ile ağ diyagramları oluşturun, düzenleyin ve paylaşın.

## 🚀 Özellikler

### 🎨 Topoloji Yönetimi
- **Görsel Editör**: Sürükle-bırak ile simge yerleştirme
- **Çoklu Simge Desteği**: Server, Switch, Router, Firewall, Cloud ve daha fazlası
- **Bağlantı Yönetimi**: Simgeler arası bağlantı oluşturma ve düzenleme
- **Arkaplan Resmi**: Topolojilere özel arkaplan resimleri ekleme
- **Alt Topolojiler**: Hiyerarşik topoloji yapısı
- **Versiyon Kontrolü**: Topoloji değişikliklerini takip etme

### 📝 Not Sistemi
- **Summernote Editör**: Zengin metin editörü ile detaylı notlar
- **Simge <PERSON>zlı Notlar**: Her simge için ayrı not alanı
- **Otomatik Kaydetme**: Değişiklikler otomatik olarak kaydedilir
- **Yazdırma Desteği**: Notları PDF olarak yazdırma

### 👥 Kullanıcı Yönetimi ve Paylaşım
- **Çoklu Kullanıcı Desteği**: Birden fazla kullanıcı sistemi
- **Paylaşım İzinleri**: Readonly ve Edit izinleri
- **Kullanıcı Arama**: Email ve kullanıcı adı ile arama
- **Paylaşım Yönetimi**: Paylaşımları görüntüleme ve kaldırma

### 🛡️ Admin Paneli
- **Kullanıcı Yönetimi**: Kullanıcıları görüntüleme, düzenleme ve silme
- **Şifre Sıfırlama**: Admin tarafından şifre sıfırlama
- **Rol Yönetimi**: Admin/User rolleri
- **İstatistikler**: Sistem kullanım istatistikleri

## 📁 Proje Yapısı

```
BT1/
├── app.py                 # Ana uygulama dosyası
├── config.py             # Konfigürasyon ayarları
├── models/               # Veritabanı modelleri
│   ├── __init__.py
│   ├── user.py          # Kullanıcı modeli
│   ├── topology.py      # Topoloji modelleri
│   └── connection.py    # Bağlantı ve simge modelleri
├── routes/              # Route modülleri
│   ├── __init__.py
│   ├── auth.py         # Kimlik doğrulama
│   ├── topology.py     # Topoloji işlemleri
│   ├── api.py          # API endpoint'leri
│   └── admin.py        # Admin işlemleri
├── templates/           # HTML template'leri
│   ├── base/
│   │   └── layout.html # Ana layout
│   ├── auth/           # Giriş/kayıt sayfaları
│   ├── topology/       # Topoloji sayfaları
│   └── admin/          # Admin sayfaları
├── static/             # Statik dosyalar
│   ├── css/
│   ├── js/
│   │   ├── canvas.js   # Canvas işlemleri
│   │   ├── notes.js    # Not editörü
│   │   └── admin.js    # Admin paneli
│   ├── images/         # Simge resimleri
│   └── uploads/        # Yüklenen dosyalar
└── utils/              # Yardımcı fonksiyonlar
    ├── __init__.py
    └── helpers.py
```

## 🛠️ Kurulum

### Gereksinimler
- Python 3.8+
- Flask
- SQLAlchemy
- Flask-Login
- Bootstrap 5
- Summernote

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd BT1
```

2. **Sanal ortam oluşturun**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

3. **Bağımlılıkları yükleyin**
```bash
pip install -r requirements.txt
```

4. **Uygulamayı başlatın**
```bash
python app.py
```

5. **Tarayıcıda açın**
```
http://localhost:3000
```

## 👤 Admin Kullanıcı

Sistem ilk çalıştırıldığında otomatik olarak bir admin kullanıcı oluşturulur:

- **Kullanıcı Adı**: `admin`
- **Email**: `<EMAIL>`
- **Şifre**: `Admin123!`
- **Rol**: `admin`

## 🎯 Kullanım

### Topoloji Oluşturma
1. Dashboard'da "Yeni Topoloji Ekle" butonuna tıklayın
2. Topoloji adını girin
3. Canvas'ta simge paletinden simgeleri sürükleyip bırakın
4. Simgeler arası bağlantı oluşturmak için "Bağlantı Oluştur" modunu açın

### Not Ekleme
1. Bir simgeye sağ tıklayın
2. "Notları Aç" seçeneğini seçin
3. Summernote editörü ile notlarınızı yazın
4. Notlar otomatik olarak kaydedilir

### Paylaşım
1. Topoloji sayfasında "Paylaş" butonuna tıklayın
2. Kullanıcı email'i veya adını arayın
3. İzin türünü seçin (Readonly/Edit)
4. "Paylaş" butonuna tıklayın

### Admin İşlemleri
1. Admin kullanıcısı ile giriş yapın
2. Üst menüden "Admin" dropdown'ını açın
3. "Kullanıcı Yönetimi" veya "Admin Dashboard" seçin
4. Kullanıcıları yönetin, şifreleri sıfırlayın

## 🔧 Konfigürasyon

`config.py` dosyasında aşağıdaki ayarları yapabilirsiniz:

- **SECRET_KEY**: Flask güvenlik anahtarı
- **DATABASE_URL**: Veritabanı bağlantı URL'si
- **UPLOAD_FOLDER**: Dosya yükleme klasörü
- **MAX_CONTENT_LENGTH**: Maksimum dosya boyutu
- **ALLOWED_EXTENSIONS**: İzin verilen dosya uzantıları

## 🚀 Üretim Ortamı

Üretim ortamında kullanım için:

1. **Güvenlik ayarlarını yapın**
```python
# config.py
SECRET_KEY = 'your-production-secret-key'
DEBUG = False
```

2. **WSGI sunucusu kullanın**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:3000 app:app
```

3. **Reverse proxy ayarlayın** (Nginx önerilir)

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 Destek

Herhangi bir sorun veya öneriniz için issue oluşturun.

---

**Geliştirici**: Ağ Topolojisi Yönetim Sistemi Ekibi  
**Versiyon**: 2.0.0  
**Son Güncelleme**: 2024
