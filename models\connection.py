"""
<PERSON>ğlantı ve simge modelleri
"""
from datetime import datetime
from . import db

class Icon(db.Model):
    """Simge modeli"""

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    icon_type = db.Column(db.String(50), nullable=False)
    x_position = db.Column(db.Float, nullable=False)
    y_position = db.Column(db.Float, nullable=False)
    topology_id = db.Column(db.Integer, db.<PERSON>Key('topology.id'), nullable=False)
    sub_topology_id = db.Column(db.Integer, db.<PERSON>ey('topology.id'), nullable=True)

    # İlişkiler
    topology = db.relationship('Topology', foreign_keys=[topology_id], backref='icons')
    sub_topology = db.relationship('Topology', foreign_keys=[sub_topology_id], backref='parent_icon', uselist=False)
    notes = db.relationship('Note', backref='icon', lazy=True, cascade='all, delete-orphan')

    # Bağlantılar için ilişkiler
    source_connections = db.relationship('Connection', foreign_keys='Connection.source_id', backref='source', lazy=True)
    target_connections = db.relationship('Connection', foreign_keys='Connection.target_id', backref='target', lazy=True)

    def __repr__(self):
        return f'<Icon {self.name}>'

class Connection(db.Model):
    """Bağlantı modeli"""

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=True)
    source_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)
    target_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)
    source_position = db.Column(db.String(20), nullable=True, default='right')  # left, right, top, bottom
    target_position = db.Column(db.String(20), nullable=True, default='left')   # left, right, top, bottom
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # İlişkiler
    topology = db.relationship('Topology', backref='connections')

    def __repr__(self):
        return f'<Connection {self.source_id}-{self.target_id}>'

class Note(db.Model):
    """Not modeli"""

    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    icon_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Note {self.icon_id}>'
