/**
 * Konva.js Tabanlı Network Topology Canvas
 * Modern zoom, pan ve connection sistemi
 */

// Template'den gelen global değişkenler
const topologyId = window.topologyId;
const canEdit = window.canEdit;

// Konva.js değişkenleri
let stage = null;
let layer = null;
let connectionLayer = null;
let tempConnectionLayer = null;

// Veri değişkenleri
let icons = [];
let connections = [];
let selectedIcon = null;
let selectedConnection = null;

// Bağlantı değişkenleri
let isConnecting = false;
let connectionStart = null;
let tempConnection = null;

// Mini map değişkenleri
let miniMapCanvas = null;
let miniMapCtx = null;
let miniMapViewport = null;

// Icon boyutları
const ICON_SIZE = 80;
const CONNECTION_POINT_SIZE = 16;

/**
 * Konva.js Canvas'ı başlat
 */
function initKonvaCanvas() {
    const container = document.getElementById('konva-container');
    if (!container) {
        console.error('Konva container bulunamadı');
        return;
    }

    // Stage oluştur
    stage = new Konva.Stage({
        container: 'konva-container',
        width: container.clientWidth,
        height: container.clientHeight,
        draggable: true // Pan için
    });

    // Ana layer (simgeler için)
    layer = new Konva.Layer();
    stage.add(layer);

    // Bağlantı layer'ı
    connectionLayer = new Konva.Layer();
    stage.add(connectionLayer);

    // Geçici bağlantı layer'ı
    tempConnectionLayer = new Konva.Layer();
    stage.add(tempConnectionLayer);

    // Zoom ve pan event'leri
    initZoomPan();

    // Mini map'i başlat
    initMiniMap();

    // Butonları başlat
    initButtons();

    // Simge paletini başlat
    initIconPalette();

    // Window resize handler
    window.addEventListener('resize', handleResize);

    // Veriyi yükle
    loadIcons();
    loadConnections();

    console.log('Konva.js canvas başlatıldı');
}

/**
 * Zoom ve Pan sistemini başlat
 */
function initZoomPan() {
    // Mouse wheel zoom
    stage.on('wheel', (e) => {
        e.evt.preventDefault();

        const oldScale = stage.scaleX();
        const pointer = stage.getPointerPosition();

        const mousePointTo = {
            x: (pointer.x - stage.x()) / oldScale,
            y: (pointer.y - stage.y()) / oldScale,
        };

        const direction = e.evt.deltaY > 0 ? -1 : 1;
        const newScale = direction > 0 ? oldScale * 1.1 : oldScale / 1.1;

        // Zoom limitlerini uygula
        const clampedScale = Math.max(0.1, Math.min(3, newScale));

        stage.scale({ x: clampedScale, y: clampedScale });

        const newPos = {
            x: pointer.x - mousePointTo.x * clampedScale,
            y: pointer.y - mousePointTo.y * clampedScale,
        };

        stage.position(newPos);
        updateZoomLevel();
        updateMiniMap();
    });

    // Zoom butonları
    document.getElementById('zoom-in').addEventListener('click', () => zoomIn());
    document.getElementById('zoom-out').addEventListener('click', () => zoomOut());
    document.getElementById('zoom-fit').addEventListener('click', () => zoomToFit());
    document.getElementById('zoom-reset').addEventListener('click', () => resetZoom());

    // Stage hareket event'i
    stage.on('dragmove', () => {
        updateMiniMap();
    });
}

/**
 * Zoom In
 */
function zoomIn() {
    const oldScale = stage.scaleX();
    const newScale = Math.min(3, oldScale * 1.2);

    const center = {
        x: stage.width() / 2,
        y: stage.height() / 2
    };

    zoomToPoint(center, newScale);
}

/**
 * Zoom Out
 */
function zoomOut() {
    const oldScale = stage.scaleX();
    const newScale = Math.max(0.1, oldScale / 1.2);

    const center = {
        x: stage.width() / 2,
        y: stage.height() / 2
    };

    zoomToPoint(center, newScale);
}

/**
 * Belirli bir noktaya zoom yap
 */
function zoomToPoint(point, newScale) {
    const oldScale = stage.scaleX();

    const mousePointTo = {
        x: (point.x - stage.x()) / oldScale,
        y: (point.y - stage.y()) / oldScale,
    };

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
        x: point.x - mousePointTo.x * newScale,
        y: point.y - mousePointTo.y * newScale,
    };

    stage.position(newPos);
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Tümünü göster
 */
function zoomToFit() {
    if (icons.length === 0) return;

    // Tüm simgelerin sınırlarını hesapla
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    icons.forEach(icon => {
        minX = Math.min(minX, icon.x_position);
        minY = Math.min(minY, icon.y_position);
        maxX = Math.max(maxX, icon.x_position + ICON_SIZE);
        maxY = Math.max(maxY, icon.y_position + ICON_SIZE);
    });

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    const scaleX = stage.width() / contentWidth;
    const scaleY = stage.height() / contentHeight;
    const scale = Math.min(scaleX, scaleY) * 0.8; // %80 doluluk

    stage.scale({ x: scale, y: scale });

    // Merkeze hizala
    const centerX = (stage.width() - contentWidth * scale) / 2 - minX * scale;
    const centerY = (stage.height() - contentHeight * scale) / 2 - minY * scale;

    stage.position({ x: centerX, y: centerY });
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Zoom'u sıfırla
 */
function resetZoom() {
    stage.scale({ x: 1, y: 1 });
    stage.position({ x: 0, y: 0 });
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Zoom seviyesi göstergesini güncelle
 */
function updateZoomLevel() {
    const zoomLevelElement = document.getElementById('zoom-level');
    if (zoomLevelElement) {
        zoomLevelElement.textContent = Math.round(stage.scaleX() * 100) + '%';
    }
}

/**
 * Window resize handler
 */
function handleResize() {
    const container = document.getElementById('konva-container');
    if (container && stage) {
        stage.width(container.clientWidth);
        stage.height(container.clientHeight);
        updateMiniMap();
    }
}

/**
 * Simgeleri yükle
 */
function loadIcons() {
    fetch(`/api/topology/${topologyId}/icons`)
        .then(response => response.json())
        .then(data => {
            // API direkt array döndürüyor
            if (Array.isArray(data)) {
                icons = data;
                displayIcons();
                console.log('Simgeler yüklendi:', icons.length, 'adet');
            } else if (data.error) {
                console.error('Simgeler yüklenemedi:', data.error);
            } else {
                console.error('Beklenmeyen API yanıtı:', data);
            }
        })
        .catch(error => {
            console.error('Simge yükleme hatası:', error);
        });
}

/**
 * Bağlantıları yükle
 */
function loadConnections() {
    fetch(`/api/topology/${topologyId}/connections`)
        .then(response => response.json())
        .then(data => {
            // API direkt array döndürüyor
            if (Array.isArray(data)) {
                connections = data;
                displayConnections();
                console.log('Bağlantılar yüklendi:', connections.length, 'adet');
            } else if (data.error) {
                console.error('Bağlantılar yüklenemedi:', data.error);
            } else {
                console.error('Beklenmeyen API yanıtı:', data);
            }
        })
        .catch(error => {
            console.error('Bağlantı yükleme hatası:', error);
        });
}

/**
 * Simgeleri görüntüle
 */
function displayIcons() {
    // Mevcut simgeleri temizle
    layer.destroyChildren();

    icons.forEach(iconData => {
        createIconNode(iconData);
    });

    updateMiniMap();
}

/**
 * Konva.js simge node'u oluştur
 */
function createIconNode(iconData) {
    // Ana grup
    const iconGroup = new Konva.Group({
        x: iconData.x_position,
        y: iconData.y_position,
        draggable: canEdit,
        id: `icon-${iconData.id}`
    });

    // n8n tarzı modern arka plan
    const background = new Konva.Rect({
        width: ICON_SIZE,
        height: ICON_SIZE,
        fill: 'white',
        stroke: '#e5e7eb',
        strokeWidth: 1,
        cornerRadius: 12, // Daha yuvarlak köşeler
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 8,
        shadowOffset: { x: 0, y: 2 },
        shadowOpacity: 0.15
    });

    // n8n tarzı icon
    const iconText = new Konva.Text({
        x: 16,
        y: 12,
        text: getIconSymbol(iconData.icon_type),
        fontSize: 28,
        fontFamily: 'Arial, sans-serif',
        fill: getIconColor(iconData.icon_type),
        width: 48,
        align: 'center'
    });

    // n8n tarzı isim
    const nameText = new Konva.Text({
        x: 8,
        y: 52,
        text: iconData.name,
        padding: 10,
        fontSize: 11,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fill: '#374151',
        fontStyle: '500', // Medium weight
        width: ICON_SIZE - 16,
        align: 'center',
        wrap: 'word'
    });

    // Bağlantı noktaları
    const connectionPoints = createConnectionPoints();

    // Grup'a ekle
    iconGroup.add(background);
    iconGroup.add(iconText);
    iconGroup.add(nameText);
    connectionPoints.forEach(point => iconGroup.add(point));

    // Event'ler
    setupIconEvents(iconGroup, iconData);

    // Layer'a ekle
    layer.add(iconGroup);
}

/**
 * Bağlantı noktalarını oluştur - n8n tarzı küçük noktalar
 */
function createConnectionPoints() {
    const points = [];
    const positions = [
        { x: -6, y: 40, position: 'left' },      // Sol orta
        { x: 86, y: 40, position: 'right' },     // Sağ orta
        { x: 40, y: -6, position: 'top' },       // Üst orta
        { x: 40, y: 86, position: 'bottom' }     // Alt orta
    ];

    positions.forEach(pos => {
        const point = new Konva.Circle({
            x: pos.x,
            y: pos.y,
            radius: 6, // n8n tarzı küçük nokta
            fill: '#6366f1',
            stroke: 'white',
            strokeWidth: 2,
            opacity: 0,
            name: 'connection-point',
            listening: true // Event'leri dinlemek için
        });

        // Position'ı custom attribute olarak ayarla
        point.setAttr('position', pos.position);

        console.log('Bağlantı noktası oluşturuldu:', pos.position, 'at', pos.x, pos.y);

        points.push(point);
    });

    console.log('Toplam bağlantı noktası sayısı:', points.length);
    return points;
}

/**
 * Simge event'lerini ayarla
 */
function setupIconEvents(iconGroup, iconData) {
    // Hover efektleri
    iconGroup.on('mouseenter', function() {
        document.body.style.cursor = canEdit ? 'move' : 'pointer';

        // Bağlantı noktalarını göster
        const connectionPoints = this.find('Circle');
        connectionPoints.forEach(point => {
            if (point.name() === 'connection-point') {
                point.opacity(0.8);
            }
        });

        layer.draw();
    });

    iconGroup.on('mouseleave', function() {
        document.body.style.cursor = 'default';

        // Bağlantı noktalarını gizle (bağlantı modunda değilse)
        if (!isConnecting) {
            const connectionPoints = this.find('Circle');
            connectionPoints.forEach(point => {
                if (point.name() === 'connection-point') {
                    point.opacity(0);
                }
            });
        }

        layer.draw();
    });

    // Tıklama
    iconGroup.on('click', function(e) {
        e.cancelBubble = true;
        selectIcon(iconData);
    });

    // Çift tıklama - isim düzenleme
    iconGroup.on('dblclick', function(e) {
        e.cancelBubble = true;
        if (canEdit) {
            editIconName(iconData);
        }
    });

    // Sağ tıklama - context menu
    iconGroup.on('contextmenu', function(e) {
        e.evt.preventDefault();
        showIconContextMenu(iconData, e.evt);
    });

    // Drag event'leri
    if (canEdit) {
        iconGroup.on('dragmove', function() {
            // Pozisyonu güncelle
            iconData.x_position = this.x();
            iconData.y_position = this.y();

            // Bağlantıları güncelle
            updateConnectionsForIcon(iconData.id);
            updateMiniMap();
        });

        iconGroup.on('dragend', function() {
            // Sunucuya pozisyonu kaydet
            updateIconPosition(iconData);
        });
    }

    // Bağlantı noktası event'leri
    const connectionPoints = iconGroup.find('Circle');
    connectionPoints.forEach(point => {
        if (point.name() === 'connection-point') {
            setupConnectionPointEvents(point, iconData);
        }
    });
}

/**
 * Icon sembolünü al
 */
function getIconSymbol(iconType) {
    const symbols = {
        'server': '🖥️',
        'switch': '🔀',
        'router': '📡',
        'firewall': '🛡️',
        'cloud': '☁️',
        'database': '🗄️',
        'workstation': '💻',
        'printer': '🖨️',
        'wireless': '📶',
        'storage': '💾',
        'loadbalancer': '⚖️'
    };
    return symbols[iconType] || '📦';
}

/**
 * n8n tarzı modern icon renkleri
 */
function getIconColor(iconType) {
    const colors = {
        'server': '#3b82f6',      // Modern mavi
        'switch': '#10b981',      // Modern yeşil
        'router': '#f59e0b',      // Modern turuncu
        'firewall': '#ef4444',    // Modern kırmızı
        'cloud': '#06b6d4',       // Modern cyan
        'database': '#8b5cf6',    // Modern mor
        'workstation': '#6366f1', // Modern indigo
        'printer': '#14b8a6',     // Modern teal
        'wireless': '#ec4899',    // Modern pembe
        'storage': '#6b7280',     // Modern gri
        'loadbalancer': '#374151' // Modern koyu gri
    };
    return colors[iconType] || '#6366f1';
}

/**
 * Bağlantı noktası event'lerini ayarla
 */
function setupConnectionPointEvents(point, iconData) {
    point.on('mousedown', function(e) {
        if (!canEdit) return;

        e.cancelBubble = true;
        console.log('Bağlantı noktası mousedown:', iconData.id, point.getAttr('position'));
        startConnection(iconData.id, point.getAttr('position'), e);
    });

    point.on('mouseup', function(e) {
        if (!canEdit || !isConnecting) return;

        e.cancelBubble = true;
        console.log('Bağlantı noktası mouseup:', iconData.id, point.getAttr('position'));
        endConnection(iconData.id, point.getAttr('position'));
    });

    point.on('mouseenter', function() {
        this.stroke('#28a745');
        this.strokeWidth(3);
        layer.draw();
    });

    point.on('mouseleave', function() {
        this.stroke('white');
        this.strokeWidth(2);
        layer.draw();
    });
}

/**
 * Bağlantı başlat
 */
function startConnection(iconId, position, e) {
    isConnecting = true;
    connectionStart = { iconId, position };

    // Tüm bağlantı noktalarını göster
    const allConnectionPoints = layer.find('Circle');
    allConnectionPoints.forEach(point => {
        if (point.name() === 'connection-point') {
            point.opacity(0.8);
        }
    });

    // Geçici bağlantı çizgisi oluştur
    createTempConnection();

    // Mouse move event'i ekle
    stage.on('mousemove', updateTempConnection);

    console.log('Bağlantı başlatıldı:', { iconId, position });
}

/**
 * Geçici bağlantı oluştur
 */
function createTempConnection() {
    const sourceIcon = icons.find(icon => icon.id === connectionStart.iconId);
    if (!sourceIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connectionStart.position);

    tempConnection = new Konva.Line({
        points: [sourcePos.x, sourcePos.y, sourcePos.x, sourcePos.y],
        stroke: '#6366f1',
        strokeWidth: 2,
        lineCap: 'round',
        opacity: 0.7 // n8n tarzı şeffaflık
    });

    tempConnectionLayer.add(tempConnection);
}

/**
 * Geçici bağlantıyı güncelle
 */
function updateTempConnection() {
    if (!tempConnection || !isConnecting) return;

    const sourceIcon = icons.find(icon => icon.id === connectionStart.iconId);
    if (!sourceIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connectionStart.position);
    const mousePos = stage.getPointerPosition();

    tempConnection.points([sourcePos.x, sourcePos.y, mousePos.x, mousePos.y]);
    tempConnectionLayer.draw();
}

/**
 * Bağlantı bitir
 */
function endConnection(targetIconId, targetPosition) {
    if (!isConnecting || !connectionStart) return;

    // Geçici bağlantıyı temizle
    if (tempConnection) {
        tempConnection.destroy();
        tempConnection = null;
    }

    // Event'i kaldır
    stage.off('mousemove', updateTempConnection);

    // Bağlantı noktalarını gizle
    const allConnectionPoints = layer.find('Circle');
    allConnectionPoints.forEach(point => {
        if (point.name() === 'connection-point') {
            point.opacity(0);
        }
    });

    // Aynı simgeye bağlantı yapılmışsa iptal et
    if (connectionStart.iconId === targetIconId) {
        resetConnectionState();
        return;
    }

    // Bağlantı oluştur
    createConnection(
        connectionStart.iconId,
        targetIconId,
        connectionStart.position,
        targetPosition
    );

    resetConnectionState();
}

/**
 * Bağlantı durumunu sıfırla
 */
function resetConnectionState() {
    isConnecting = false;
    connectionStart = null;

    if (tempConnection) {
        tempConnection.destroy();
        tempConnection = null;
    }

    stage.off('mousemove', updateTempConnection);
    tempConnectionLayer.draw();
}

/**
 * Bağlantı noktası pozisyonunu hesapla - n8n tarzı
 */
function getConnectionPointPosition(iconData, position) {
    const positions = {
        'left': { x: iconData.x_position - 6, y: iconData.y_position + 40 },      // Sol orta
        'right': { x: iconData.x_position + 86, y: iconData.y_position + 40 },    // Sağ orta
        'top': { x: iconData.x_position + 40, y: iconData.y_position - 6 },       // Üst orta
        'bottom': { x: iconData.x_position + 40, y: iconData.y_position + 86 }    // Alt orta
    };

    return positions[position] || { x: iconData.x_position + 40, y: iconData.y_position + 40 };
}

/**
 * Bağlantıları görüntüle
 */
function displayConnections() {
    // Mevcut bağlantıları temizle
    connectionLayer.destroyChildren();

    connections.forEach(connection => {
        createConnectionLine(connection);
    });

    updateMiniMap();
}

/**
 * n8n tarzı bağlantı çizgisi oluştur
 */
function createConnectionLine(connection) {
    const sourceIcon = icons.find(icon => icon.id === connection.source_id);
    const targetIcon = icons.find(icon => icon.id === connection.target_id);

    if (!sourceIcon || !targetIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connection.source_position || 'right');
    const targetPos = getConnectionPointPosition(targetIcon, connection.target_position || 'left');

    // n8n tarzı smooth bezier curve hesapla
    const dx = targetPos.x - sourcePos.x;
    const dy = targetPos.y - sourcePos.y;

    // Daha akıllı control point hesaplama
    let controlOffset = Math.max(Math.abs(dx) * 0.5, 50);
    if (Math.abs(dx) < 100) controlOffset = 80; // Minimum curve

    // Yön bazlı control point ayarlama
    let cp1x = sourcePos.x, cp1y = sourcePos.y;
    let cp2x = targetPos.x, cp2y = targetPos.y;

    if (connection.source_position === 'right') cp1x += controlOffset;
    else if (connection.source_position === 'left') cp1x -= controlOffset;
    else if (connection.source_position === 'bottom') cp1y += controlOffset;
    else if (connection.source_position === 'top') cp1y -= controlOffset;

    if (connection.target_position === 'left') cp2x -= controlOffset;
    else if (connection.target_position === 'right') cp2x += controlOffset;
    else if (connection.target_position === 'top') cp2y -= controlOffset;
    else if (connection.target_position === 'bottom') cp2y += controlOffset;

    const line = new Konva.Line({
        points: [
            sourcePos.x, sourcePos.y,
            cp1x, cp1y,
            cp2x, cp2y,
            targetPos.x, targetPos.y
        ],
        stroke: '#6366f1', // n8n tarzı mor renk
        strokeWidth: 2,
        tension: 0,
        bezier: true,
        id: `connection-${connection.id}`,
        lineCap: 'round',
        lineJoin: 'round'
    });

    // n8n tarzı bağlantı event'leri
    line.on('click', function(e) {
        e.cancelBubble = true;
        selectConnection(connection);
    });

    line.on('mouseenter', function() {
        this.strokeWidth(3);
        this.stroke('#4f46e5');
        connectionLayer.draw();
        document.body.style.cursor = 'pointer';
    });

    line.on('mouseleave', function() {
        this.strokeWidth(2);
        this.stroke('#6366f1');
        connectionLayer.draw();
        document.body.style.cursor = 'default';
    });

    connectionLayer.add(line);
}

/**
 * API fonksiyonları ve yardımcı fonksiyonlar
 */

/**
 * Bağlantı oluştur
 */
function createConnection(sourceId, targetId, sourcePosition, targetPosition) {
    fetch('/api/connections', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            source_id: sourceId,
            target_id: targetId,
            source_position: sourcePosition,
            target_position: targetPosition,
            topology_id: topologyId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadConnections(); // Bağlantıları yeniden yükle
            //showNotification('Bağlantı başarıyla oluşturuldu.', 'success');
        } else {
            console.error('Bağlantı oluşturulamadı:', data.error);
            showNotification('Bağlantı oluşturulamadı: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Bağlantı oluşturma hatası:', error);
        showNotification('Bağlantı oluşturma hatası: ' + error.message, 'error');
    });
}

/**
 * Simge pozisyonunu güncelle
 */
function updateIconPosition(iconData) {
    fetch(`/api/icons/${iconData.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            x_position: iconData.x_position,
            y_position: iconData.y_position
        })
    })
    .catch(error => {
        console.error('Simge konumu güncellenirken hata:', error);
    });
}

/**
 * Simge seç
 */
function selectIcon(iconData) {
    selectedIcon = iconData;
    selectedConnection = null;
    console.log('Simge seçildi:', iconData.name);
}

/**
 * Bağlantı seç
 */
function selectConnection(connection) {
    selectedConnection = connection;
    selectedIcon = null;
    console.log('Bağlantı seçildi:', connection.id);
}

/**
 * Simge ismini düzenle
 */
function editIconName(iconData) {
    const newName = prompt('Yeni simge adı:', iconData.name);
    if (newName && newName !== iconData.name) {
        fetch(`/api/icons/${iconData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: newName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                iconData.name = newName;
                loadIcons(); // Simgeleri yeniden yükle
                showNotification('Simge adı güncellendi.', 'success');
            } else {
                showNotification('Simge güncellenirken hata oluştu: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Simge güncelleme hatası:', error);
            showNotification('Simge güncellenirken hata oluştu.', 'error');
        });
    }
}

/**
 * Context menu göster
 */
function showIconContextMenu(iconData, event) {
    // Basit context menu implementasyonu
    const actions = [
        {
            text: 'Notları Aç',
            action: () => openNotes(iconData)
        },
        {
            text: 'Düzenle',
            action: () => editIconName(iconData)
        },
        {
            text: 'Sil',
            action: () => deleteIcon(iconData)
        }
    ];

    // Basit alert ile context menu (daha sonra geliştirilebilir)
    const choice = prompt('Seçenekler:\n1. Notları Aç\n2. Düzenle\n3. Sil\n\nSeçiminizi yapın (1-3):');

    if (choice === '1') openNotes(iconData);
    else if (choice === '2') editIconName(iconData);
    else if (choice === '3') deleteIcon(iconData);
}

/**
 * Simge notlarını aç
 */
function openNotes(iconData) {
    const url = `/notes/${topologyId}/${iconData.id}`;
    window.open(url, '_blank');
}

/**
 * Simgeyi sil
 */
function deleteIcon(iconData) {
    if (confirm(`"${iconData.name}" simgesini silmek istediğinizden emin misiniz?`)) {
        fetch(`/api/icons/${iconData.id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadIcons(); // Simgeleri yeniden yükle
                loadConnections(); // Bağlantıları yeniden yükle
                //showNotification('Simge silindi.', 'success');
            } else {
                showNotification('Simge silinirken hata oluştu: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Simge silme hatası:', error);
            showNotification('Simge silinirken hata oluştu.', 'error');
        });
    }
}

/**
 * Bildirim göster
 */
function showNotification(message, type = 'info') {
    // Basit alert (daha sonra toast notification ile değiştirilebilir)
    alert(message);
}

/**
 * Simge için bağlantıları güncelle
 */
function updateConnectionsForIcon(iconId) {
    // Bağlantıları yeniden çiz
    displayConnections();
}

/**
 * Mini map'i başlat
 */
function initMiniMap() {
    miniMapCanvas = document.getElementById('mini-map-canvas');
    miniMapViewport = document.getElementById('mini-map-viewport');

    if (!miniMapCanvas) return;

    miniMapCtx = miniMapCanvas.getContext('2d');

    // Canvas boyutlarını ayarla
    const rect = miniMapCanvas.getBoundingClientRect();
    miniMapCanvas.width = rect.width * 2; // Retina için
    miniMapCanvas.height = rect.height * 2;
    miniMapCtx.scale(2, 2);

    // Mini map tıklama
    miniMapCanvas.addEventListener('click', function(e) {
        const rect = miniMapCanvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;

        // Stage'i o pozisyona taşı
        panToPosition(x, y);
    });

    updateMiniMap();
}

/**
 * Mini map'i güncelle
 */
function updateMiniMap() {
    if (!miniMapCtx) return;

    const canvas = miniMapCanvas;
    const ctx = miniMapCtx;

    // Temizle
    ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);

    // Arka plan
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width / 2, canvas.height / 2);

    // Simgeleri çiz
    const scaleX = (canvas.width / 2) / 4000; // Canvas genişliği
    const scaleY = (canvas.height / 2) / 3000; // Canvas yüksekliği

    icons.forEach(icon => {
        const x = icon.x_position * scaleX;
        const y = icon.y_position * scaleY;
        const size = ICON_SIZE * Math.min(scaleX, scaleY);

        ctx.fillStyle = '#007bff';
        ctx.fillRect(x, y, Math.max(2, size), Math.max(2, size));
    });

    // Bağlantıları çiz
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 1;
    connections.forEach(connection => {
        const sourceIcon = icons.find(icon => icon.id === connection.source_id);
        const targetIcon = icons.find(icon => icon.id === connection.target_id);

        if (sourceIcon && targetIcon) {
            const x1 = (sourceIcon.x_position + ICON_SIZE/2) * scaleX;
            const y1 = (sourceIcon.y_position + ICON_SIZE/2) * scaleY;
            const x2 = (targetIcon.x_position + ICON_SIZE/2) * scaleX;
            const y2 = (targetIcon.y_position + ICON_SIZE/2) * scaleY;

            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    });

    updateMiniMapViewport();
}

/**
 * Mini map viewport'unu güncelle
 */
function updateMiniMapViewport() {
    if (!miniMapViewport || !stage) return;

    const stageBox = stage.getClientRect();
    const scale = stage.scaleX();

    const miniMapWidth = miniMapCanvas.clientWidth;
    const miniMapHeight = miniMapCanvas.clientHeight;

    // Viewport boyutları
    const viewportWidth = (stage.width() / scale / 4000) * miniMapWidth;
    const viewportHeight = (stage.height() / scale / 3000) * miniMapHeight;

    // Viewport pozisyonu
    const viewportX = (-stage.x() / scale / 4000) * miniMapWidth;
    const viewportY = (-stage.y() / scale / 3000) * miniMapHeight;

    miniMapViewport.style.left = Math.max(0, Math.min(miniMapWidth - viewportWidth, viewportX)) + 'px';
    miniMapViewport.style.top = Math.max(0, Math.min(miniMapHeight - viewportHeight, viewportY)) + 'px';
    miniMapViewport.style.width = Math.min(miniMapWidth, viewportWidth) + 'px';
    miniMapViewport.style.height = Math.min(miniMapHeight, viewportHeight) + 'px';
}

/**
 * Belirli pozisyona git
 */
function panToPosition(x, y) {
    const targetX = x * 4000; // Canvas genişliği
    const targetY = y * 3000; // Canvas yüksekliği

    // Merkeze hizala
    const newX = stage.width() / 2 - targetX * stage.scaleX();
    const newY = stage.height() / 2 - targetY * stage.scaleY();

    stage.position({ x: newX, y: newY });
    updateMiniMap();
}

/**
 * Butonları başlat
 */
function initButtons() {
    // Eski bağlantı modu butonu artık gerekmiyor
    const connectionBtn = document.getElementById('toggle-connection-mode');
    if (connectionBtn) {
        connectionBtn.style.display = 'none';
    }
}

/**
 * Simge paletini başlat
 */
function initIconPalette() {
    const toggleButton = document.getElementById('toggle-palette');
    const palette = document.getElementById('icon-palette');

    if (toggleButton && palette) {
        toggleButton.addEventListener('click', function() {
            if (palette.style.display === 'none') {
                palette.style.display = 'flex';
            } else {
                palette.style.display = 'none';
            }
        });

        // Simge palette öğelerine event listener ekle
        const paletteItems = palette.querySelectorAll('.icon-palette-item');
        paletteItems.forEach(item => {
            item.addEventListener('click', function() {
                const iconType = this.dataset.type;
                addIconToCanvas(iconType);
            });
        });
    }
}

/**
 * Canvas'a simge ekle
 */
function addIconToCanvas(iconType) {
    if (!canEdit) {
        showNotification('Bu topolojiyi düzenleme yetkiniz yok.', 'warning');
        return;
    }

    // Varsayılan isim oluştur
    const defaultName = getDefaultIconName(iconType);

    const iconData = {
        name: defaultName,
        icon_type: iconType,
        x_position: Math.random() * 300 + 50, // Rastgele konum
        y_position: Math.random() * 200 + 50,
        topology_id: topologyId
    };

    fetch('/api/icons', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(iconData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadIcons(); // Simgeleri yeniden yükle
            //showNotification('Simge eklendi.', 'success');
        } else {
            showNotification('Simge eklenirken hata oluştu: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Simge ekleme hatası:', error);
        showNotification('Simge eklenirken hata oluştu.', 'error');
    });
}

/**
 * Varsayılan simge adını al
 */
function getDefaultIconName(iconType) {
    const names = {
        'server': 'Server',
        'switch': 'Switch',
        'router': 'Router',
        'firewall': 'Firewall',
        'cloud': 'Cloud',
        'database': 'Database',
        'workstation': 'Workstation',
        'printer': 'Printer',
        'wireless': 'Wireless',
        'storage': 'Storage',
        'loadbalancer': 'Load Balancer'
    };
    return names[iconType] || 'Device';
}

// Sayfa yüklendiğinde başlat
document.addEventListener('DOMContentLoaded', function() {
    initKonvaCanvas();
});
