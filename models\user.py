"""
Kullanıcı modeli
"""
from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from . import db

class User(UserMixin, db.Model):
    """Kullanıcı modeli"""
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.String(20), default='user')  # 'admin' veya 'user'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active_user = db.Column(db.<PERSON>, default=True)
    
    # İlişkiler
    topologies = db.relationship('Topology', backref='owner', lazy=True)
    shared_topologies = db.relationship('TopologyShare', foreign_keys='TopologyShare.user_id', backref='user', lazy=True)

    def set_password(self, password):
        """Şifreyi hash'leyerek kaydet"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Şifreyi kontrol et"""
        return check_password_hash(self.password_hash, password)

    def is_authenticated(self):
        """Kullanıcı kimlik doğrulaması yapılmış mı?"""
        return True

    def is_active(self):
        """Kullanıcı aktif mi?"""
        return self.is_active_user

    def is_anonymous(self):
        """Kullanıcı anonim mi?"""
        return False

    def get_id(self):
        """Kullanıcı ID'sini string olarak döndür"""
        return str(self.id)
    
    def is_admin(self):
        """Kullanıcı admin mi?"""
        return self.role == 'admin'

    def __repr__(self):
        return f'<User {self.username}>'
