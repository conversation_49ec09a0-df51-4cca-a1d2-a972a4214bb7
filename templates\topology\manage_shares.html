{% extends "base/layout.html" %}

{% block title %}{{ topology.name }} - Paylaşım Yönetimi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2>{{ topology.name }} - Paylaşım Yönetimi</h2>
                <a href="{{ url_for('topology.view_topology', topology_id=topology.id) }}" class="btn btn-light">
                    Topolojiye Dön
                </a>
            </div>
            <div class="card-body">
                <!-- Kullanıcı Arama ve Paylaşım -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h4><PERSON><PERSON></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userSearch" class="form-label"><PERSON><PERSON><PERSON><PERSON><PERSON> (Email veya Kullanıcı Adı)</label>
                                    <input type="text" class="form-control" id="userSearch" placeholder="En az 2 karakter girin...">
                                    <div id="searchResults" class="mt-2"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="permissionSelect" class="form-label">İzin Türü</label>
                                    <select class="form-select" id="permissionSelect">
                                        <option value="readonly">Sadece Okuma</option>
                                        <option value="edit">Düzenleme</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-success d-block" id="shareBtn" disabled>
                                        Paylaş
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Mevcut Paylaşımlar -->
                <div class="row">
                    <div class="col-md-12">
                        <h4>Mevcut Paylaşımlar</h4>
                        <div id="sharesList">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Yükleniyor...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Paylaşım Kaldırma Modal -->
<div class="modal fade" id="removeShareModal" tabindex="-1" aria-labelledby="removeShareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeShareModalLabel">Paylaşımı Kaldır</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bu kullanıcıyla paylaşımı kaldırmak istediğinizden emin misiniz?</p>
                <p><strong id="removeUserInfo"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveBtn">Kaldır</button>
            </div>
        </div>
    </div>
</div>

<script>
const topologyId = {{ topology.id }};
let selectedUserId = null;
let shareToRemove = null;

// Kullanıcı arama
let searchTimeout;
document.getElementById('userSearch').addEventListener('input', function() {
    const query = this.value.trim();
    const resultsDiv = document.getElementById('searchResults');
    
    clearTimeout(searchTimeout);
    
    if (query.length < 2) {
        resultsDiv.innerHTML = '';
        selectedUserId = null;
        updateShareButton();
        return;
    }
    
    searchTimeout = setTimeout(() => {
        fetch(`/api/users/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.users);
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('Arama hatası:', error);
                resultsDiv.innerHTML = '<div class="alert alert-danger">Arama sırasında bir hata oluştu.</div>';
            });
    }, 300);
});

function displaySearchResults(users) {
    const resultsDiv = document.getElementById('searchResults');
    
    if (users.length === 0) {
        resultsDiv.innerHTML = '<div class="alert alert-info">Kullanıcı bulunamadı.</div>';
        return;
    }
    
    let html = '<div class="list-group">';
    users.forEach(user => {
        html += `
            <button type="button" class="list-group-item list-group-item-action" onclick="selectUser(${user.id}, '${user.username}', '${user.email}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${user.username}</h6>
                </div>
                <p class="mb-1">${user.email}</p>
            </button>
        `;
    });
    html += '</div>';
    
    resultsDiv.innerHTML = html;
}

function selectUser(userId, username, email) {
    selectedUserId = userId;
    document.getElementById('userSearch').value = `${username} (${email})`;
    document.getElementById('searchResults').innerHTML = '';
    updateShareButton();
}

function updateShareButton() {
    document.getElementById('shareBtn').disabled = !selectedUserId;
}

// Paylaşım işlemi
document.getElementById('shareBtn').addEventListener('click', function() {
    if (!selectedUserId) return;
    
    const permission = document.getElementById('permissionSelect').value;
    
    fetch(`/api/topology/${topologyId}/share`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user_id: selectedUserId,
            permission: permission
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Başarı mesajı göster
            showAlert('success', data.message);
            
            // Formu temizle
            document.getElementById('userSearch').value = '';
            document.getElementById('searchResults').innerHTML = '';
            selectedUserId = null;
            updateShareButton();
            
            // Paylaşım listesini yenile
            loadShares();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Paylaşım hatası:', error);
        showAlert('danger', 'Paylaşım sırasında bir hata oluştu.');
    });
});

// Paylaşımları yükle
function loadShares() {
    fetch(`/api/topology/${topologyId}/shares`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayShares(data.shares);
            } else {
                document.getElementById('sharesList').innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            }
        })
        .catch(error => {
            console.error('Paylaşımlar yüklenirken hata:', error);
            document.getElementById('sharesList').innerHTML = '<div class="alert alert-danger">Paylaşımlar yüklenirken bir hata oluştu.</div>';
        });
}

function displayShares(shares) {
    const sharesDiv = document.getElementById('sharesList');
    
    if (shares.length === 0) {
        sharesDiv.innerHTML = '<div class="alert alert-info">Henüz kimseyle paylaşılmamış.</div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>Kullanıcı</th><th>Email</th><th>İzin</th><th>Paylaşım Tarihi</th><th>İşlemler</th></tr></thead><tbody>';
    
    shares.forEach(share => {
        const permissionBadge = share.permission === 'edit' 
            ? '<span class="badge bg-warning text-dark">Düzenleme</span>'
            : '<span class="badge bg-secondary">Sadece Okuma</span>';
        
        const shareDate = new Date(share.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        html += `
            <tr>
                <td>${share.username}</td>
                <td>${share.email}</td>
                <td>${permissionBadge}</td>
                <td>${shareDate}</td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeShare(${share.id}, '${share.username}')">
                        Kaldır
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    sharesDiv.innerHTML = html;
}

function removeShare(shareId, username) {
    shareToRemove = shareId;
    document.getElementById('removeUserInfo').textContent = username;
    new bootstrap.Modal(document.getElementById('removeShareModal')).show();
}

// Paylaşım kaldırma onayı
document.getElementById('confirmRemoveBtn').addEventListener('click', function() {
    if (!shareToRemove) return;
    
    fetch(`/api/topology/${topologyId}/share/${shareToRemove}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadShares();
        } else {
            showAlert('danger', data.error);
        }
        
        // Modal'ı kapat
        bootstrap.Modal.getInstance(document.getElementById('removeShareModal')).hide();
        shareToRemove = null;
    })
    .catch(error => {
        console.error('Paylaşım kaldırma hatası:', error);
        showAlert('danger', 'Paylaşım kaldırılırken bir hata oluştu.');
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Sayfanın üstüne ekle
    document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.card-body').firstChild);
    
    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Sayfa yüklendiğinde paylaşımları getir
document.addEventListener('DOMContentLoaded', function() {
    loadShares();
});
</script>
{% endblock %}
