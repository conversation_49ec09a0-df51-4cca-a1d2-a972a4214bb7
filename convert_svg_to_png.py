import os
import cairosvg

# SVG dosyalarını PNG'ye d<PERSON><PERSON>
def convert_svg_to_png():
    image_dir = os.path.join('static', 'images')
    svg_files = [f for f in os.listdir(image_dir) if f.endswith('.svg')]
    
    for svg_file in svg_files:
        svg_path = os.path.join(image_dir, svg_file)
        png_path = os.path.join(image_dir, svg_file.replace('.svg', '.png'))
        
        try:
            cairosvg.svg2png(url=svg_path, write_to=png_path, output_width=50, output_height=50)
            print(f"Converted {svg_file} to PNG")
        except Exception as e:
            print(f"Error converting {svg_file}: {e}")

if __name__ == "__main__":
    convert_svg_to_png()
