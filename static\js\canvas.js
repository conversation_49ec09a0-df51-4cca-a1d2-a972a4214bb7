/**
 * Konva.js Tabanlı Network Topology Canvas
 * Modern zoom, pan ve connection sistemi
 */

// Template'den gelen global değişkenler
const topologyId = window.topologyId;
const canEdit = window.canEdit;

// Konva.js değişkenleri
let stage = null;
let layer = null;
let connectionLayer = null;
let tempConnectionLayer = null;

// Veri değişkenleri
let icons = [];
let connections = [];
let selectedIcon = null;
let selectedIcons = []; // Çoklu seçim için
let selectedConnection = null;

// Bağlantı değişkenleri
let isConnecting = false;
let connectionStart = null;
let tempConnection = null;

// Mini map değişkenleri
let miniMapCanvas = null;
let miniMapCtx = null;
let miniMapViewport = null;

// Çoklu seçim değişkenleri
let isMultiSelectMode = false; // Ctrl tuşu basılı mı?
let selectionRectangle = null; // Seçim dikdörtgeni
let isSelecting = false; // Seçim yapılıyor mu?
let selectionStart = null; // Seçim başlangıç noktası

// Pan modu değişkenleri
let isPanMode = false; // Space tuşu basılı mı?
let isPanning = false; // Pan yapılıyor mu?
let panStart = null; // Pan başlangıç noktası

// Icon boyutları - Gerçek n8n tarzı
const ICON_SIZE = 60;        // Kare simge (80x80)
const ICON_TEXT_HEIGHT = 20; // Alt kısımdaki text alanı
const CONNECTION_POINT_SIZE = 8;

/**
 * Konva.js Canvas'ı başlat
 */
function initKonvaCanvas() {
    const container = document.getElementById('konva-container');
    if (!container) {
        console.error('Konva container bulunamadı');
        return;
    }

    // Container boyutlarını zorla ayarla
    const containerWidth = container.clientWidth || window.innerWidth - 100;
    const containerHeight = container.clientHeight || window.innerHeight - 200;

    console.log('Container boyutları:', containerWidth, 'x', containerHeight);

    // Stage oluştur
    stage = new Konva.Stage({
        container: 'konva-container',
        width: containerWidth,
        height: containerHeight,
        draggable: true // Pan için
    });

    // Ana layer (simgeler için)
    layer = new Konva.Layer();
    stage.add(layer);

    // Bağlantı layer'ı
    connectionLayer = new Konva.Layer();
    stage.add(connectionLayer);

    // Geçici bağlantı layer'ı
    tempConnectionLayer = new Konva.Layer();
    stage.add(tempConnectionLayer);

    // Zoom ve pan event'leri
    initZoomPan();

    // Mini map'i başlat
    initMiniMap();

    // Butonları başlat
    initButtons();

    // Simge paletini başlat
    initIconPalette();

    // Window resize handler
    window.addEventListener('resize', handleResize);

    // Veriyi yükle
    loadIcons();
    loadConnections();

    // Sayfa yüklendiğinde tümünü göster
    setTimeout(() => {
        zoomToFit();
    }, 500); // Veriler yüklendikten sonra

    console.log('Konva.js canvas başlatıldı');
}

/**
 * Zoom ve Pan sistemini başlat
 */
function initZoomPan() {
    // Mouse wheel zoom
    stage.on('wheel', (e) => {
        e.evt.preventDefault();

        const oldScale = stage.scaleX();
        const pointer = stage.getPointerPosition();

        const mousePointTo = {
            x: (pointer.x - stage.x()) / oldScale,
            y: (pointer.y - stage.y()) / oldScale,
        };

        const direction = e.evt.deltaY > 0 ? -1 : 1;
        const newScale = direction > 0 ? oldScale * 1.1 : oldScale / 1.1;

        // Zoom limitlerini uygula
        const clampedScale = Math.max(0.1, Math.min(3, newScale));

        stage.scale({ x: clampedScale, y: clampedScale });

        const newPos = {
            x: pointer.x - mousePointTo.x * clampedScale,
            y: pointer.y - mousePointTo.y * clampedScale,
        };

        stage.position(newPos);
        updateZoomLevel();
        updateMiniMap();
    });

    // Zoom butonları
    document.getElementById('zoom-in').addEventListener('click', () => zoomIn());
    document.getElementById('zoom-out').addEventListener('click', () => zoomOut());
    document.getElementById('zoom-fit').addEventListener('click', () => zoomToFit());
    document.getElementById('zoom-reset').addEventListener('click', () => resetZoom());

    // Stage hareket event'i
    stage.on('dragmove', () => {
        updateMiniMap();
    });

    // Çoklu seçim için stage event'leri
    setupMultiSelectEvents();
}

/**
 * Zoom In
 */
function zoomIn() {
    const oldScale = stage.scaleX();
    const newScale = Math.min(3, oldScale * 1.2);

    const center = {
        x: stage.width() / 2,
        y: stage.height() / 2
    };

    zoomToPoint(center, newScale);
}

/**
 * Zoom Out
 */
function zoomOut() {
    const oldScale = stage.scaleX();
    const newScale = Math.max(0.1, oldScale / 1.2);

    const center = {
        x: stage.width() / 2,
        y: stage.height() / 2
    };

    zoomToPoint(center, newScale);
}

/**
 * Belirli bir noktaya zoom yap
 */
function zoomToPoint(point, newScale) {
    const oldScale = stage.scaleX();

    const mousePointTo = {
        x: (point.x - stage.x()) / oldScale,
        y: (point.y - stage.y()) / oldScale,
    };

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
        x: point.x - mousePointTo.x * newScale,
        y: point.y - mousePointTo.y * newScale,
    };

    stage.position(newPos);
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Tümünü göster
 */
function zoomToFit() {
    if (icons.length === 0) return;

    // Tüm simgelerin sınırlarını hesapla
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    icons.forEach(icon => {
        minX = Math.min(minX, icon.x_position);
        minY = Math.min(minY, icon.y_position);
        maxX = Math.max(maxX, icon.x_position + ICON_SIZE);
        maxY = Math.max(maxY, icon.y_position + ICON_SIZE);
    });

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    const scaleX = stage.width() / contentWidth;
    const scaleY = stage.height() / contentHeight;
    const scale = Math.min(scaleX, scaleY) * 0.8; // %80 doluluk

    stage.scale({ x: scale, y: scale });

    // Merkeze hizala
    const centerX = (stage.width() - contentWidth * scale) / 2 - minX * scale;
    const centerY = (stage.height() - contentHeight * scale) / 2 - minY * scale;

    stage.position({ x: centerX, y: centerY });
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Zoom'u sıfırla
 */
function resetZoom() {
    stage.scale({ x: 1, y: 1 });
    stage.position({ x: 0, y: 0 });
    updateZoomLevel();
    updateMiniMap();
}

/**
 * Zoom seviyesi göstergesini güncelle
 */
function updateZoomLevel() {
    const zoomLevelElement = document.getElementById('zoom-level');
    if (zoomLevelElement) {
        zoomLevelElement.textContent = Math.round(stage.scaleX() * 100) + '%';
    }
}

/**
 * Window resize handler
 */
function handleResize() {
    const container = document.getElementById('konva-container');
    if (container && stage) {
        const newWidth = container.clientWidth || window.innerWidth - 100;
        const newHeight = container.clientHeight || window.innerHeight - 200;

        console.log('Resize - yeni boyutlar:', newWidth, 'x', newHeight);

        if (newWidth > 0 && newHeight > 0) {
            stage.width(newWidth);
            stage.height(newHeight);
            updateMiniMap();
        }
    }
}

/**
 * Simgeleri yükle
 */
function loadIcons() {
    fetch(`/api/topology/${topologyId}/icons`)
        .then(response => response.json())
        .then(data => {
            // API direkt array döndürüyor
            if (Array.isArray(data)) {
                icons = data;
                displayIcons();
                console.log('Simgeler yüklendi:', icons.length, 'adet');
            } else if (data.error) {
                console.error('Simgeler yüklenemedi:', data.error);
            } else {
                console.error('Beklenmeyen API yanıtı:', data);
            }
        })
        .catch(error => {
            console.error('Simge yükleme hatası:', error);
        });
}

/**
 * Bağlantıları yükle
 */
function loadConnections() {
    fetch(`/api/topology/${topologyId}/connections`)
        .then(response => response.json())
        .then(data => {
            // API direkt array döndürüyor
            if (Array.isArray(data)) {
                connections = data;
                displayConnections();
                console.log('Bağlantılar yüklendi:', connections.length, 'adet');
            } else if (data.error) {
                console.error('Bağlantılar yüklenemedi:', data.error);
            } else {
                console.error('Beklenmeyen API yanıtı:', data);
            }
        })
        .catch(error => {
            console.error('Bağlantı yükleme hatası:', error);
        });
}

/**
 * Simgeleri görüntüle
 */
function displayIcons() {
    // Mevcut simgeleri temizle
    layer.destroyChildren();

    icons.forEach(iconData => {
        createIconNode(iconData);
    });

    // Tüm layer'ı çiz
    layer.draw();
    updateMiniMap();
}

/**
 * Konva.js simge node'u oluştur
 */
function createIconNode(iconData) {
    // Ana grup
    const iconGroup = new Konva.Group({
        x: iconData.x_position,
        y: iconData.y_position,
        draggable: canEdit,
        id: `icon-${iconData.id}`
    });

    // IconData'yı gruba ekle (global mouseup için)
    iconGroup.setAttr('iconData', iconData);

    // Ana simge arka planı - Gerçek n8n tarzı renkli kare
    const iconBackground = new Konva.Rect({
        x: 0,
        y: 0,
        width: ICON_SIZE,
        height: ICON_SIZE,
        fill: getIconBackgroundColor(iconData.icon_type),
        cornerRadius: 8,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
        shadowOffset: { x: 0, y: 2 }
    });

    // Bootstrap Icons - Palette ile birebir aynı
    const iconText = new Konva.Text({
        x: 0,
        y: 0,
        width: ICON_SIZE,
        height: ICON_SIZE,
        text: getBootstrapIconUnicode(iconData.icon_type),
        fontSize: 40,
        fontFamily: 'bootstrap-icons',
        fill: 'white',
        align: 'center',
        verticalAlign: 'middle'
    });

    // Alt kısımda simge adı - n8n tarzı küçük text
    const nameText = new Konva.Text({
        x: -20, // Sola kaydırdık
        y: ICON_SIZE + 2, // Boşluğu azalttık
        width: ICON_SIZE + 40, // Genişliği artırdık (120px)
        height: ICON_TEXT_HEIGHT + 10, // Yüksekliği artırdık
        text: iconData.name,
        fontSize: 11,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fill: '#374151',
        align: 'center',
        verticalAlign: 'top',
        wrap: 'word'
    });

    // Bağlantı noktaları
    const connectionPoints = createConnectionPoints();

    // Grup'a ekle (bağlantı noktalarını en son ekle ki üstte görünsün)
    iconGroup.add(iconBackground);
    iconGroup.add(iconText);
    iconGroup.add(nameText);

    // Bağlantı noktalarını en son ekle
    connectionPoints.forEach((point, index) => {
        const originalX = point.getAttr('originalX');
        const originalY = point.getAttr('originalY');
        const position = point.getAttr('position');

        iconGroup.add(point);

        // Gruba eklendikten sonra pozisyonu custom attribute'lardan ayarla
        point.x(originalX);
        point.y(originalY);

        // Bağlantı noktasını en üste çıkar (çizgilerin üzerinde görünsün)
        point.moveToTop();

        // Bağlantı noktası drag'ı devre dışı bırak
        point.draggable(false);

        // Bağlantı noktası event'lerini ekle
        setupConnectionPointEvents(point, iconData);
    });

    // Event'ler
    setupIconEvents(iconGroup, iconData, nameText, iconBackground, iconText);

    // Layer'a ekle
    layer.add(iconGroup);
}

/**
 * Bağlantı noktalarını oluştur - n8n tarzı küçük noktalar
 */
function createConnectionPoints() {
    const points = [];
    const centerY = ICON_SIZE / 2; // 40px (80/2)
    const centerX = ICON_SIZE / 2; // 40px (80/2)

    const positions = [
        { x: -3, y: centerY, position: 'left' },           // Sol kenar orta
        { x: ICON_SIZE + 3, y: centerY, position: 'right' }, // Sağ kenar orta
        { x: centerX, y: -3, position: 'top' },            // Üst kenar orta
        { x: centerX, y: ICON_SIZE + 3, position: 'bottom' } // Alt kenar orta (sadece simge alanı)
    ];

    positions.forEach(pos => {
        const point = new Konva.Circle({
            radius: 4, // n8n tarzı küçük nokta
            fill: '#6366f1',
            stroke: 'white',
            strokeWidth: 1,
            opacity: 0, // Başlangıçta gizli
            name: 'connection-point',
            listening: true // Event'leri dinlemek için
        });

        // Position'ı custom attribute olarak ayarla
        point.setAttr('position', pos.position);
        point.setAttr('originalX', pos.x);
        point.setAttr('originalY', pos.y);

        points.push(point);
    });
    return points;
}

/**
 * Simge event'lerini ayarla
 */
function setupIconEvents(iconGroup, iconData, nameText, iconBackground, iconText) {
    // n8n tarzı hover efektleri
    iconGroup.on('mouseenter', function() {
        document.body.style.cursor = canEdit ? 'move' : 'pointer';

        // Bağlantı noktalarını n8n tarzında göster
        const connectionPoints = this.find('Circle');
        connectionPoints.forEach(point => {
            if (point.name() === 'connection-point') {
                point.opacity(0.8); // Subtle görünürlük
                point.radius(4); // Biraz büyüt
            }
        });

        layer.draw();
    });

    iconGroup.on('mouseleave', function() {
        document.body.style.cursor = 'default';

        // Bağlantı noktalarını n8n tarzında gizle
        if (!isConnecting) {
            const connectionPoints = this.find('Circle');
            connectionPoints.forEach(point => {
                if (point.name() === 'connection-point') {
                    point.opacity(0); // Tamamen gizle
                    point.radius(4); // Normal boyut
                }
            });
        }

        layer.draw();
    });

    // Tıklama
    iconGroup.on('click', function(e) {
        e.cancelBubble = true;

        // Çoklu seçim kontrolü
        if (isMultiSelectMode) {
            // Ctrl modunda: seçimi toggle et
            const existingIndex = selectedIcons.findIndex(icon => icon.id === iconData.id);
            if (existingIndex >= 0) {
                // Zaten seçili, kaldır
                selectedIcons.splice(existingIndex, 1);
            } else {
                // Seçili değil, ekle
                selectedIcons.push(iconData);
            }
            selectedIcon = selectedIcons.length > 0 ? selectedIcons[selectedIcons.length - 1] : null;
        } else {
            // Normal mod: tek seçim
            selectedIcon = iconData;
            selectedIcons = [iconData];
        }

        // Simge seçim görselini güncelle
        updateIconSelectionVisuals();

        // Simge bilgilerini göster
        if (selectedIcon) {
            showIconInfo(selectedIcon);
        }
    });

    // Icon'a çift tıklama - alt topoloji oluştur
    iconBackground.on('dblclick', function(e) {
        e.cancelBubble = true;
        createSubTopology(iconData);
    });

    iconText.on('dblclick', function(e) {
        e.cancelBubble = true;
        createSubTopology(iconData);
    });

    // Name'e çift tıklama - isim düzenleme
    nameText.on('dblclick', function(e) {
        e.cancelBubble = true;
        if (canEdit) {
            startInlineEdit(iconData, nameText);
        }
    });

    // Sağ tıklama - context menu
    iconGroup.on('contextmenu', function(e) {
        e.evt.preventDefault();
        showIconContextMenu(iconData, e.evt);
    });

    // Drag event'leri
    if (canEdit) {
        let dragStartPos = null;

        iconGroup.on('dragstart', function() {
            // Drag başlangıç pozisyonunu kaydet
            dragStartPos = {
                x: this.x(),
                y: this.y()
            };

            // Eğer bu icon seçili değilse, sadece bunu seç
            if (!selectedIcons.find(icon => icon.id === iconData.id)) {
                selectedIcon = iconData;
                selectedIcons = [iconData];
                updateIconSelectionVisuals();
            }
        });

        iconGroup.on('dragmove', function() {
            if (!dragStartPos) return;

            // Bu icon'un hareket miktarını hesapla
            const deltaX = this.x() - dragStartPos.x;
            const deltaY = this.y() - dragStartPos.y;

            // Seçili diğer iconları da aynı miktarda hareket ettir
            selectedIcons.forEach(selectedIconData => {
                if (selectedIconData.id !== iconData.id) {
                    const otherGroup = layer.findOne(`#icon-${selectedIconData.id}`);
                    if (otherGroup) {
                        otherGroup.x(selectedIconData.x_position + deltaX);
                        otherGroup.y(selectedIconData.y_position + deltaY);
                    }
                }
            });

            // Pozisyonu güncelle
            iconData.x_position = this.x();
            iconData.y_position = this.y();

            // Bağlantıları güncelle
            updateConnectionsForIcon(iconData.id);
            updateMiniMap();
            layer.draw();
        });

        iconGroup.on('dragend', function() {
            if (!dragStartPos) return;

            // Bu icon'un hareket miktarını hesapla
            const deltaX = this.x() - dragStartPos.x;
            const deltaY = this.y() - dragStartPos.y;

            // Tüm seçili iconların pozisyonlarını güncelle
            selectedIcons.forEach(selectedIconData => {
                if (selectedIconData.id !== iconData.id) {
                    selectedIconData.x_position += deltaX;
                    selectedIconData.y_position += deltaY;
                }

                // Sunucuya pozisyonu kaydet
                updateIconPosition(selectedIconData);
            });

            dragStartPos = null;
        });
    }

    // Bağlantı noktası event'leri zaten createIconNode'da ayarlandı
}

/**
 * Bootstrap Icons gerçek Unicode karakterleri
 */
function getBootstrapIconUnicode(iconType) {
    // Bootstrap Icons font'undan gerçek Unicode karakterler
    const icons = {
        'server': '\uF40F',        // bi-hdd-rack
        'switch': '\uF6D5',        // bi-ethernet
        'router': '\uF6EC',        // bi-router
        'firewall': '\uF538',      // bi-shield-lock
        'cloud': '\uF2C1',         // bi-cloud
        'database': '\uF8B0',      // bi-database
        'workstation': '\uF6A6',   // bi-pc-display
        'printer': '\uF501',       // bi-printer
        'wireless': '\uF61C',      // bi-wifi
        'storage': '\uF6F9',       // bi-device-hdd
        'loadbalancer': '\uF2EE'   // bi-diagram-3
    };

    return icons[iconType] || icons['server'];
}

/**
 * Palette ile uyumlu simgeler
 */
function getIconSymbol(iconType) {
    const symbols = {
        'server': '▦',         // Server rack benzeri
        'switch': '⧈',         // Ethernet/Switch benzeri
        'router': '⟐',         // Router benzeri
        'firewall': '⬟',       // Shield benzeri
        'cloud': '☁',          // Cloud
        'database': '⬢',       // Database benzeri
        'workstation': '▣',    // PC Display benzeri
        'printer': '⬚',        // Printer benzeri
        'wireless': '◉',       // WiFi benzeri
        'storage': '⬛',        // HDD benzeri
        'loadbalancer': '⬡'    // Diagram benzeri
    };
    return symbols[iconType] || '▦';
}

/**
 * n8n tarzı arka plan renkleri - %20 transparanlık ile
 */
function getIconBackgroundColor(iconType) {
    const colors = {
        'server': 'rgba(59, 130, 246, 0.8)',      // Mavi %20 transparent
        'switch': 'rgba(16, 185, 129, 0.8)',      // Yeşil %20 transparent
        'router': 'rgba(245, 158, 11, 0.8)',      // Turuncu %20 transparent
        'firewall': 'rgba(239, 68, 68, 0.8)',     // Kırmızı %20 transparent
        'cloud': 'rgba(6, 182, 212, 0.8)',        // Cyan %20 transparent
        'database': 'rgba(139, 92, 246, 0.8)',    // Mor %20 transparent
        'workstation': 'rgba(99, 102, 241, 0.8)', // İndigo %20 transparent
        'printer': 'rgba(20, 184, 166, 0.8)',     // Teal %20 transparent
        'wireless': 'rgba(236, 72, 153, 0.8)',    // Pembe %20 transparent
        'storage': 'rgba(107, 114, 128, 0.8)',    // Gri %20 transparent
        'loadbalancer': 'rgba(55, 65, 81, 0.8)'   // Koyu gri %20 transparent
    };
    return colors[iconType] || 'rgba(99, 102, 241, 0.8)';
}

/**
 * n8n tarzı icon renkleri (artık hep beyaz)
 */
function getIconColor(iconType) {
    return 'white'; // n8n'de tüm ikonlar beyaz
}

/**
 * Bağlantı noktası event'lerini ayarla
 */
function setupConnectionPointEvents(point, iconData) {
    point.on('mousedown', function(e) {
        console.log('🔴 mousedown event çalıştı');
        if (!canEdit) {
            console.log('❌ canEdit false');
            return;
        }

        e.cancelBubble = true;
        e.evt.stopPropagation(); // Simge drag'ını engelle

        const position = point.getAttr('position');
        console.log('🔴 mousedown:', iconData.id, position);
        startConnection(iconData.id, position, e);
    });

    point.on('mouseup', function(e) {
        console.log('🟢 mouseup event çalıştı');
        if (!canEdit) {
            console.log('❌ canEdit false');
            return;
        }
        if (!isConnecting) {
            console.log('❌ isConnecting false');
            return;
        }

        e.cancelBubble = true;
        e.evt.stopPropagation(); // Simge drag'ını engelle

        const position = point.getAttr('position');
        console.log('🎯 Bağlantı noktası mouseup:', iconData.id, position);
        endConnection(iconData.id, position);
    });

    point.on('mouseenter', function() {
        this.fill('#4f46e5'); // Koyu indigo
        this.stroke('#ffffff');
        this.strokeWidth(2);
        this.radius(4); // Büyüt
        this.opacity(1); // Tam görünür
        layer.draw();
    });

    point.on('mouseleave', function() {
        this.fill('#6366f1'); // Normal indigo
        this.stroke('white');
        this.strokeWidth(1);
        this.radius(4); // Normal boyut
        this.opacity(0.8); // Subtle
        layer.draw();
    });
}

/**
 * Bağlantı başlat
 */
function startConnection(iconId, position, e) {
    isConnecting = true;
    connectionStart = { iconId, position };

    // Tüm simgelerin drag'ını devre dışı bırak
    const allGroups = layer.find('Group');
    allGroups.forEach(group => {
        group.draggable(false);
    });

    // Tüm bağlantı noktalarını göster
    const allConnectionPoints = layer.find('Circle');
    allConnectionPoints.forEach(point => {
        if (point.name() === 'connection-point') {
            point.opacity(0.8);
        }
    });

    // Geçici bağlantı çizgisi oluştur
    createTempConnection();

    // Mouse move event'i ekle
    stage.on('mousemove', updateTempConnection);

    // Global mouseup event ekle
    stage.on('mouseup', handleGlobalMouseUp);
}

/**
 * Geçici bağlantı oluştur
 */
function createTempConnection() {
    const sourceIcon = icons.find(icon => icon.id === connectionStart.iconId);
    if (!sourceIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connectionStart.position);

    tempConnection = new Konva.Line({
        points: [sourcePos.x, sourcePos.y, sourcePos.x, sourcePos.y, sourcePos.x, sourcePos.y, sourcePos.x, sourcePos.y],
        stroke: '#6366f1',
        strokeWidth: 2,
        lineCap: 'round',
        opacity: 0.7,
        bezier: true, // Bezier curve aktif
        dash: [5, 5] // Kesikli çizgi
    });

    tempConnectionLayer.add(tempConnection);
    tempConnectionLayer.draw();
}

/**
 * Geçici bağlantıyı güncelle
 */
function updateTempConnection() {
    if (!tempConnection || !isConnecting) return;

    const sourceIcon = icons.find(icon => icon.id === connectionStart.iconId);
    if (!sourceIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connectionStart.position);
    const rawMousePos = stage.getPointerPosition();

    // Mouse pozisyonunu stage transform'una göre düzelt
    const transform = stage.getAbsoluteTransform().copy();
    transform.invert();
    const mousePos = transform.point(rawMousePos);

    // n8n tarzı bezier curve
    const dx = mousePos.x - sourcePos.x;
    const dy = mousePos.y - sourcePos.y;

    // Control point hesaplama (yön bazlı)
    let controlOffset = Math.max(Math.abs(dx) * 0.5, 80);

    let cp1x = sourcePos.x;
    let cp1y = sourcePos.y;
    let cp2x = mousePos.x;
    let cp2y = mousePos.y;

    // Başlangıç pozisyonuna göre control point
    if (connectionStart.position === 'right') cp1x += controlOffset;
    else if (connectionStart.position === 'left') cp1x -= controlOffset;
    else if (connectionStart.position === 'bottom') cp1y += controlOffset;
    else if (connectionStart.position === 'top') cp1y -= controlOffset;

    // Mouse pozisyonuna göre control point (varsayılan left)
    cp2x -= controlOffset;

    tempConnection.points([
        sourcePos.x, sourcePos.y,  // Start point
        cp1x, cp1y,                // Control point 1
        cp2x, cp2y,                // Control point 2
        mousePos.x, mousePos.y     // End point
    ]);

    tempConnectionLayer.draw();
}

/**
 * Global mouseup handler
 */
function handleGlobalMouseUp(e) {
    console.log('🌍 Global mouseup event');
    if (!isConnecting) return;

    // Mouse pozisyonunu stage transform'una göre düzelt
    const rawMousePos = stage.getPointerPosition();
    const transform = stage.getAbsoluteTransform().copy();
    transform.invert();
    const mousePos = transform.point(rawMousePos);

    console.log('🔍 Mouse pozisyonları:', {
        raw: rawMousePos,
        transformed: mousePos,
        scale: stage.scaleX()
    });

    // Manuel olarak bağlantı noktalarını kontrol et
    const allConnectionPoints = layer.find('Circle');
    let targetPoint = null;
    let targetIconData = null;
    let targetPosition = null;

    for (let point of allConnectionPoints) {
        if (point.name() === 'connection-point') {
            // Bağlantı noktasının global pozisyonunu hesapla
            const pointGlobalPos = point.getAbsolutePosition();
            const distance = Math.sqrt(
                Math.pow(rawMousePos.x - pointGlobalPos.x, 2) +
                Math.pow(rawMousePos.y - pointGlobalPos.y, 2)
            );

            // 15 pixel tolerans
            if (distance <= 15) {
                targetPoint = point;
                const targetIconGroup = point.getParent();
                targetIconData = targetIconGroup.getAttr('iconData');
                targetPosition = point.getAttr('position');
                console.log('🎯 Manuel bağlantı noktası bulundu:', {
                    distance: distance,
                    iconId: targetIconData.id,
                    position: targetPosition
                });
                break;
            }
        }
    }

    if (targetPoint && targetIconData) {
        console.log('🎯 Hedef simge:', targetIconData.id, targetPosition);
        endConnection(targetIconData.id, targetPosition);
        return;
    }

    // Bağlantı noktası bulunamadı, iptal et
    console.log('❌ Bağlantı iptal edildi - manuel kontrol sonucu');
    resetConnectionState();
}

/**
 * Bağlantı bitir
 */
function endConnection(targetIconId, targetPosition) {
    if (!isConnecting || !connectionStart) {
        return;
    }

    // Aynı simgeye bağlantı yapılmışsa iptal et
    if (connectionStart.iconId === targetIconId) {
        resetConnectionState();
        return;
    }

    // Position'ları string'e çevir
    let sourcePos = connectionStart.position;
    let targetPos = targetPosition;

    if (typeof sourcePos === 'object') {
        if (sourcePos.x === -8) sourcePos = 'left';
        else if (sourcePos.x === 88) sourcePos = 'right';
        else if (sourcePos.y === -8) sourcePos = 'top';
        else if (sourcePos.y === 88) sourcePos = 'bottom';
        else sourcePos = 'right';
    }

    if (typeof targetPos === 'object') {
        if (targetPos.x === -8) targetPos = 'left';
        else if (targetPos.x === 88) targetPos = 'right';
        else if (targetPos.y === -8) targetPos = 'top';
        else if (targetPos.y === 88) targetPos = 'bottom';
        else targetPos = 'left';
    }

    // Bağlantı oluştur
    createConnection(
        connectionStart.iconId,
        targetIconId,
        sourcePos,
        targetPos
    );

    resetConnectionState();
}

/**
 * Bağlantı durumunu sıfırla
 */
function resetConnectionState() {
    isConnecting = false;
    connectionStart = null;

    // Geçici bağlantıyı temizle
    if (tempConnection) {
        tempConnection.destroy();
        tempConnection = null;
    }

    // Event'leri kaldır
    stage.off('mousemove', updateTempConnection);
    stage.off('mouseup', handleGlobalMouseUp);

    // Tüm simgelerin drag'ını geri aç
    if (canEdit) {
        const allGroups = layer.find('Group');
        allGroups.forEach(group => {
            group.draggable(true);
        });
    }

    // Bağlantı noktalarını gizle
    const allConnectionPoints = layer.find('Circle');
    allConnectionPoints.forEach(point => {
        if (point.name() === 'connection-point') {
            point.opacity(0);
        }
    });

    layer.draw();
    tempConnectionLayer.draw();
}

/**
 * Bağlantı noktası pozisyonunu hesapla - n8n tarzı
 */
function getConnectionPointPosition(iconData, position) {
    // Eğer position bir obje ise, string'e çevir
    if (typeof position === 'object' && position !== null) {
        // Position objesinden string position'ı çıkar
        if (position.x === -6) position = 'left';
        else if (position.x === ICON_SIZE + 6) position = 'right';
        else if (position.y === -6) position = 'top';
        else if (position.y === ICON_SIZE + 6) position = 'bottom';
        else position = 'right'; // varsayılan
    }

    const centerY = ICON_SIZE / 2; // 40px
    const centerX = ICON_SIZE / 2; // 40px

    const positions = {
        'left': { x: iconData.x_position - 6, y: iconData.y_position + centerY },           // Sol kenar orta
        'right': { x: iconData.x_position + ICON_SIZE + 6, y: iconData.y_position + centerY }, // Sağ kenar orta
        'top': { x: iconData.x_position + centerX, y: iconData.y_position - 6 },           // Üst kenar orta
        'bottom': { x: iconData.x_position + centerX, y: iconData.y_position + ICON_SIZE + 6 } // Alt kenar orta
    };

    return positions[position] || { x: iconData.x_position + centerX, y: iconData.y_position + centerY };
}

/**
 * Bağlantıları görüntüle
 */
function displayConnections() {
    // Mevcut bağlantıları temizle
    connectionLayer.destroyChildren();

    connections.forEach(connection => {
        createConnectionLine(connection);
    });

    updateMiniMap();
}

/**
 * n8n tarzı bağlantı çizgisi oluştur
 */
function createConnectionLine(connection) {
    const sourceIcon = icons.find(icon => icon.id === connection.source_id);
    const targetIcon = icons.find(icon => icon.id === connection.target_id);

    if (!sourceIcon || !targetIcon) return;

    const sourcePos = getConnectionPointPosition(sourceIcon, connection.source_position || 'right');
    const targetPos = getConnectionPointPosition(targetIcon, connection.target_position || 'left');

    // n8n tarzı smooth bezier curve hesapla
    const dx = targetPos.x - sourcePos.x;
    const dy = targetPos.y - sourcePos.y;

    // Daha akıllı control point hesaplama
    let controlOffset = Math.max(Math.abs(dx) * 0.5, 50);
    if (Math.abs(dx) < 100) controlOffset = 80; // Minimum curve

    // Yön bazlı control point ayarlama
    let cp1x = sourcePos.x, cp1y = sourcePos.y;
    let cp2x = targetPos.x, cp2y = targetPos.y;

    if (connection.source_position === 'right') cp1x += controlOffset;
    else if (connection.source_position === 'left') cp1x -= controlOffset;
    else if (connection.source_position === 'bottom') cp1y += controlOffset;
    else if (connection.source_position === 'top') cp1y -= controlOffset;

    if (connection.target_position === 'left') cp2x -= controlOffset;
    else if (connection.target_position === 'right') cp2x += controlOffset;
    else if (connection.target_position === 'top') cp2y -= controlOffset;
    else if (connection.target_position === 'bottom') cp2y += controlOffset;

    // Bağlantı grubu oluştur
    const connectionGroup = new Konva.Group({
        id: `connection-group-${connection.id}`,
        name: 'connection-group'
    });

    // Bağlantı çizgisi
    const line = new Konva.Line({
        points: [
            sourcePos.x, sourcePos.y,
            cp1x, cp1y,
            cp2x, cp2y,
            targetPos.x, targetPos.y
        ],
        stroke: '#6366f1', // n8n tarzı mor renk
        strokeWidth: 2,
        tension: 0,
        bezier: true,
        id: `connection-${connection.id}`,
        lineCap: 'round',
        lineJoin: 'round',
        name: 'connection-line'
    });

    // Bağlantı ortası pozisyonu hesapla (label için)
    const midX = (sourcePos.x + targetPos.x) / 2;
    const midY = (sourcePos.y + targetPos.y) / 2;

    // Label arka planı
    const labelBackground = new Konva.Rect({
        x: midX - 40,
        y: midY - 10,
        width: 80,
        height: 20,
        fill: 'white',
        stroke: '#e5e7eb',
        strokeWidth: 1,
        cornerRadius: 4,
        opacity: connection.name ? 0.9 : 0,
        name: 'label-background'
    });

    // Bağlantı label'ı
    const connectionLabel = new Konva.Text({
        x: midX - 40,
        y: midY - 10,
        width: 80,
        height: 20,
        text: connection.name || '',
        fontSize: 11,
        fontFamily: 'Arial, sans-serif',
        fill: '#374151',
        align: 'center',
        verticalAlign: 'middle',
        padding: 4,
        name: 'connection-label'
    });

    // Grup'a ekle
    connectionGroup.add(line);
    connectionGroup.add(labelBackground);
    connectionGroup.add(connectionLabel);

    // Connection data'yı grup'a ekle
    connectionGroup.setAttr('connectionData', connection);

    // Event'leri ayarla
    setupConnectionEvents(connectionGroup, connection, connectionLabel);

    connectionLayer.add(connectionGroup);
}

/**
 * Bağlantı event'lerini ayarla
 */
function setupConnectionEvents(connectionGroup, connection, connectionLabel) {
    const line = connectionGroup.findOne('.connection-line');
    const labelBackground = connectionGroup.findOne('.label-background');

    // Bağlantı seçimi
    connectionGroup.on('click', function(e) {
        e.cancelBubble = true;
        selectConnection(connection);
        console.log('🔗 Bağlantı seçildi:', connection.id);
    });

    // Hover efektleri
    connectionGroup.on('mouseenter', function() {
        line.strokeWidth(3);
        line.stroke('#4f46e5');
        connectionLayer.draw();
        document.body.style.cursor = 'pointer';
    });

    connectionGroup.on('mouseleave', function() {
        line.strokeWidth(2);
        line.stroke('#6366f1');
        connectionLayer.draw();
        document.body.style.cursor = 'default';
    });

    // Label'a çift tıklama - isim düzenleme
    connectionLabel.on('dblclick', function(e) {
        e.cancelBubble = true;
        startConnectionLabelEdit(connection, connectionLabel, labelBackground);
    });

    labelBackground.on('dblclick', function(e) {
        e.cancelBubble = true;
        startConnectionLabelEdit(connection, connectionLabel, labelBackground);
    });
}

/**
 * Bağlantı label düzenlemeyi başlat
 */
function startConnectionLabelEdit(connection, connectionLabel, labelBackground) {
    // Mevcut label'ı gizle
    connectionLabel.hide();
    labelBackground.hide();

    // HTML input elementi oluştur
    const input = document.createElement('input');
    input.type = 'text';
    input.value = connection.name || '';
    input.placeholder = 'Bağlantı adı...';
    input.style.position = 'absolute';
    input.style.fontSize = '11px';
    input.style.fontFamily = 'Arial, sans-serif';
    input.style.textAlign = 'center';
    input.style.border = '2px solid #6366f1';
    input.style.borderRadius = '4px';
    input.style.padding = '2px 4px';
    input.style.background = 'white';
    input.style.zIndex = '10000';
    input.style.width = '80px';
    input.style.height = '16px';

    // Pozisyonu hesapla - canvas container'a göre
    const globalPos = connectionLabel.getAbsolutePosition();
    const canvasContainer = document.getElementById('konva-container');
    const containerRect = canvasContainer.getBoundingClientRect();

    input.style.left = (containerRect.left + globalPos.x) + 'px';
    input.style.top = (containerRect.top + globalPos.y) + 'px';

    // DOM'a ekle
    document.body.appendChild(input);
    input.focus();
    input.select();

    // Enter tuşu ile kaydet
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            finishConnectionLabelEdit(connection, connectionLabel, labelBackground, input);
        } else if (e.key === 'Escape') {
            cancelConnectionLabelEdit(connectionLabel, labelBackground, input);
        }
    });

    // Focus kaybında kaydet
    input.addEventListener('blur', function() {
        finishConnectionLabelEdit(connection, connectionLabel, labelBackground, input);
    });
}

/**
 * Bağlantı label düzenlemeyi bitir
 */
function finishConnectionLabelEdit(connection, connectionLabel, labelBackground, input) {
    const newName = input.value.trim();

    // Backend'e gönder
    fetch(`/api/connections/${connection.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: newName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Label'ı güncelle
            connection.name = newName;
            connectionLabel.text(newName);

            // Arka planı göster/gizle
            if (newName) {
                labelBackground.opacity(0.9);
            } else {
                labelBackground.opacity(0);
            }

            connectionLabel.show();
            labelBackground.show();
            connectionLayer.draw();
        } else {
            alert('Bağlantı adı güncellenemedi: ' + data.error);
            connectionLabel.show();
            labelBackground.show();
        }
    })
    .catch(error => {
        console.error('Bağlantı adı güncelleme hatası:', error);
        alert('Bağlantı adı güncellenirken bir hata oluştu.');
        connectionLabel.show();
        labelBackground.show();
    });

    // Input'u kaldır
    document.body.removeChild(input);
    connectionLayer.draw();
}

/**
 * Bağlantı label düzenlemeyi iptal et
 */
function cancelConnectionLabelEdit(connectionLabel, labelBackground, input) {
    connectionLabel.show();
    labelBackground.show();
    document.body.removeChild(input);
    connectionLayer.draw();
}

/**
 * API fonksiyonları ve yardımcı fonksiyonlar
 */

/**
 * Bağlantı oluştur
 */
function createConnection(sourceId, targetId, sourcePosition, targetPosition) {
    console.log('📡 API çağrısı yapılıyor:', {
        source_id: sourceId,
        target_id: targetId,
        source_position: sourcePosition,
        target_position: targetPosition,
        topology_id: topologyId
    });

    fetch('/api/connections', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            source_id: sourceId,
            target_id: targetId,
            source_position: sourcePosition,
            target_position: targetPosition,
            topology_id: topologyId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadConnections(); // Bağlantıları yeniden yükle
           // showNotification('Bağlantı başarıyla oluşturuldu.', 'success');
        } else {
            console.error('Bağlantı oluşturulamadı:', data.error);
            showNotification('Bağlantı oluşturulamadı: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Bağlantı oluşturma hatası:', error);
        showNotification('Bağlantı oluşturma hatası: ' + error.message, 'error');
    });
}

/**
 * Simge pozisyonunu güncelle
 */
function updateIconPosition(iconData) {
    fetch(`/api/icons/${iconData.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            x_position: iconData.x_position,
            y_position: iconData.y_position
        })
    })
    .catch(error => {
        console.error('Simge konumu güncellenirken hata:', error);
    });
}

/**
 * Simge seç
 */
function selectIcon(iconData) {
    selectedIcon = iconData;
    selectedConnection = null;
    console.log('Simge seçildi:', iconData.name);
}

/**
 * Bağlantı seç
 */
function selectConnection(connection) {
    selectedConnection = connection;
    selectedIcon = null;
    console.log('Bağlantı seçildi:', connection.id);
}

/**
 * Bağlantıyı sil
 */
function deleteConnection(connection) {
    if (confirm(`"${connection.name || 'Bağlantı'}" bağlantısını silmek istediğinizden emin misiniz?`)) {
        fetch(`/api/connections/${connection.id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Seçimi temizle
                selectedConnection = null;

                // Bağlantıları yeniden yükle
                loadConnections();
                //showNotification('Bağlantı silindi.', 'success');
                console.log('✅ Bağlantı silindi:', connection.id);
            } else {
                showNotification('Bağlantı silinirken hata oluştu: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Bağlantı silme hatası:', error);
            showNotification('Bağlantı silinirken hata oluştu.', 'error');
        });
    }
}

/**
 * Bağlantıyı sil
 */
function deleteConnection(connection) {
    if (confirm(`"${connection.name || 'Bağlantı'}" bağlantısını silmek istediğinizden emin misiniz?`)) {
        fetch(`/api/connections/${connection.id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Seçimi temizle
                selectedConnection = null;

                // Bağlantıları yeniden yükle
                loadConnections();
                //showNotification('Bağlantı silindi.', 'success');
                console.log('✅ Bağlantı silindi:', connection.id);
            } else {
                showNotification('Bağlantı silinirken hata oluştu: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Bağlantı silme hatası:', error);
            showNotification('Bağlantı silinirken hata oluştu.', 'error');
        });
    }
}

/**
 * Inline edit başlat
 */
function startInlineEdit(iconData, nameText) {
    // Mevcut text'i gizle
    nameText.hide();

    // HTML input elementi oluştur
    const input = document.createElement('input');
    input.type = 'text';
    input.value = iconData.name;
    input.style.position = 'absolute';
    input.style.fontSize = '11px';
    input.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    input.style.textAlign = 'center';
    input.style.border = '2px solid #007bff';
    input.style.borderRadius = '4px';
    input.style.padding = '2px 4px';
    input.style.background = 'white';
    input.style.zIndex = '10000';

    // Pozisyonu hesapla - canvas container'a göre
    const iconGroup = nameText.getParent();
    const globalPos = iconGroup.getAbsolutePosition();
    const canvasContainer = document.getElementById('konva-container');
    const containerRect = canvasContainer.getBoundingClientRect();

    input.style.left = (containerRect.left + globalPos.x) + 'px';
    input.style.top = (containerRect.top + globalPos.y + ICON_SIZE + 5) + 'px';
    input.style.width = ICON_SIZE + 'px';
    input.style.height = (ICON_TEXT_HEIGHT - 5) + 'px';

    // DOM'a ekle
    document.body.appendChild(input);
    input.focus();
    input.select();

    // Enter tuşu ile kaydet
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            finishInlineEdit(iconData, nameText, input);
        } else if (e.key === 'Escape') {
            cancelInlineEdit(nameText, input);
        }
    });

    // Focus kaybında kaydet
    input.addEventListener('blur', function() {
        finishInlineEdit(iconData, nameText, input);
    });
}

/**
 * Inline edit'i bitir ve kaydet
 */
function finishInlineEdit(iconData, nameText, input) {
    const newName = input.value.trim();

    if (newName && newName !== iconData.name) {
        iconData.name = newName;

        // Sunucuya gönder
        fetch(`/api/icons/${iconData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: iconData.name
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Text'i güncelle
                nameText.text(iconData.name);
                nameText.show();
                layer.draw();
            } else {
                alert('Simge adı güncellenemedi: ' + data.error);
                nameText.show();
            }
        })
        .catch(error => {
            console.error('Simge adı güncelleme hatası:', error);
            alert('Simge adı güncellenirken bir hata oluştu.');
            nameText.show();
        });
    } else {
        nameText.show();
    }

    // Input'u kaldır
    document.body.removeChild(input);
    layer.draw();
}

/**
 * Inline edit'i iptal et
 */
function cancelInlineEdit(nameText, input) {
    nameText.show();
    document.body.removeChild(input);
    layer.draw();
}

/**
 * Simge ismini düzenle (context menu için)
 */
function editIconName(iconData) {
    const newName = prompt('Yeni simge adı:', iconData.name);
    if (newName && newName !== iconData.name) {
        fetch(`/api/icons/${iconData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: newName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                iconData.name = newName;
                loadIcons(); // Simgeleri yeniden yükle
                showNotification('Simge adı güncellendi.', 'success');
            } else {
                showNotification('Simge güncellenirken hata oluştu: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Simge güncelleme hatası:', error);
            showNotification('Simge güncellenirken hata oluştu.', 'error');
        });
    }
}

/**
 * Context menu göster
 */
function showIconContextMenu(iconData, event) {
    const contextMenu = document.getElementById('context-menu');

    // Menü pozisyonunu ayarla
    contextMenu.style.left = event.clientX + 'px';
    contextMenu.style.top = event.clientY + 'px';
    contextMenu.style.display = 'block';

    // Çoklu seçim durumuna göre menü göster/gizle
    const singleMenu = contextMenu.querySelector('.single-selection-menu');
    const multiMenu = contextMenu.querySelector('.multi-selection-menu');

    if (selectedIcons.length > 1) {
        if (singleMenu) singleMenu.style.display = 'none';
        if (multiMenu) multiMenu.style.display = 'block';
    } else {
        if (singleMenu) singleMenu.style.display = 'block';
        if (multiMenu) multiMenu.style.display = 'none';
    }

    // Mevcut event listener'ları temizle
    const items = contextMenu.querySelectorAll('.context-menu-item');
    items.forEach(item => {
        item.replaceWith(item.cloneNode(true));
    });

    // Yeni event listener'ları ekle
    document.getElementById('context-open-notes').addEventListener('click', () => {
        hideContextMenu();
        openNotes(iconData);
    });

    document.getElementById('context-edit-name').addEventListener('click', () => {
        hideContextMenu();
        editIconName(iconData);
    });

    document.getElementById('context-delete').addEventListener('click', () => {
        hideContextMenu();
        if (selectedIcons.length > 1) {
            deleteSelectedIcons();
        } else {
            deleteIcon(iconData);
        }
    });

    // Çoklu seçim hizalama event listener'ları
    setupAlignmentEventListeners();

    // Menü dışına tıklandığında kapat
    setTimeout(() => {
        document.addEventListener('click', hideContextMenu, { once: true });
    }, 10);
}

/**
 * Context menu gizle
 */
function hideContextMenu() {
    const contextMenu = document.getElementById('context-menu');
    contextMenu.style.display = 'none';
}

/**
 * Simge notlarını aç
 */
function openNotes(iconData) {
    const url = `/notes/${topologyId}/${iconData.id}`;
    window.open(url, '_blank');
}

/**
 * Simgeyi sil
 */
function deleteIcon(iconData) {
    if (confirm(`"${iconData.name}" simgesini silmek istediğinizden emin misiniz?`)) {
        fetch(`/api/icons/${iconData.id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadIcons(); // Simgeleri yeniden yükle
                loadConnections(); // Bağlantıları yeniden yükle
                //showNotification('Simge silindi.', 'success');
            } else {
                alert('Simge silinirken hata oluştu: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Simge silme hatası:', error);
            alert('Simge silinirken hata oluştu.');
        });
    }
}

/**
 * Alt topoloji oluştur veya mevcut alt topolojiye git
 */
function createSubTopology(iconData) {
    // Önce simgenin zaten alt topolojisi var mı kontrol et
    fetch(`/api/icons/${iconData.id}/sub_topology`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.sub_topology_id) {
                // Zaten alt topoloji var, direkt yönlendir
                window.location.href = `/topology/${data.sub_topology_id}`;
            } else {
                // Alt topoloji yok, yeni oluştur
                createNewSubTopology(iconData);
            }
        })
        .catch(error => {
            console.error('Alt topoloji kontrol hatası:', error);
            // Hata durumunda yeni oluşturmaya devam et
            createNewSubTopology(iconData);
        });
}

/**
 * Yeni alt topoloji oluştur
 */
function createNewSubTopology(iconData) {
    const subTopologyName = `${iconData.name} - Alt Topoloji`;

    // Sadece oluşturma onayı iste
    if (confirm(`"${iconData.name}" için alt topoloji oluşturulsun mu?`)) {
        fetch('/topology/create_sub', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                parent_icon_id: iconData.id,
                parent_topology_id: topologyId,
                name: subTopologyName,
                description: `${iconData.name} simgesi için oluşturulan alt topoloji`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Başarılı ise direkt yönlendir, bildirim gösterme
                window.location.href = `/topology/${data.topology_id}`;
            } else {
                // Sadece hata durumunda alert göster
                alert(`Hata: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Alt topoloji oluşturma hatası:', error);
            alert('Alt topoloji oluşturulurken bir hata oluştu.');
        });
    }
}

/**
 * Simge bilgilerini göster
 */
function showIconInfo(iconData) {
    // Simge bilgilerini console'da göster (daha sonra UI paneli eklenebilir)
    console.log('📋 Seçili Simge:', {
        id: iconData.id,
        name: iconData.name,
        type: iconData.icon_type,
        position: `(${iconData.x_position}, ${iconData.y_position})`
    });

    // Gelecekte buraya bir info panel eklenebilir
    // updateInfoPanel(iconData);
}

/**
 * Bildirim göster
 */
function showNotification(message, type = 'info') {
    // Basit alert (daha sonra toast notification ile değiştirilebilir)
    alert(message);
}

/**
 * Simge için bağlantıları güncelle
 */
function updateConnectionsForIcon(iconId) {
    // Bağlantıları yeniden çiz
    displayConnections();
}

/**
 * Mini map'i başlat
 */
function initMiniMap() {
    miniMapCanvas = document.getElementById('mini-map-canvas');
    miniMapViewport = document.getElementById('mini-map-viewport');

    if (!miniMapCanvas) return;

    miniMapCtx = miniMapCanvas.getContext('2d');

    // Canvas boyutlarını ayarla
    const rect = miniMapCanvas.getBoundingClientRect();
    miniMapCanvas.width = rect.width * 2; // Retina için
    miniMapCanvas.height = rect.height * 2;
    miniMapCtx.scale(2, 2);

    // Mini map tıklama
    miniMapCanvas.addEventListener('click', function(e) {
        const rect = miniMapCanvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;

        // Stage'i o pozisyona taşı
        panToPosition(x, y);
    });

    updateMiniMap();
}

/**
 * Mini map'i güncelle
 */
function updateMiniMap() {
    if (!miniMapCtx) return;

    const canvas = miniMapCanvas;
    const ctx = miniMapCtx;

    // Temizle
    ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);

    // Arka plan
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width / 2, canvas.height / 2);

    // Simgeleri çiz
    const scaleX = (canvas.width / 2) / 4000; // Canvas genişliği
    const scaleY = (canvas.height / 2) / 3000; // Canvas yüksekliği

    icons.forEach(icon => {
        const x = icon.x_position * scaleX;
        const y = icon.y_position * scaleY;
        const size = ICON_SIZE * Math.min(scaleX, scaleY);

        ctx.fillStyle = '#007bff';
        ctx.fillRect(x, y, Math.max(2, size), Math.max(2, size));
    });

    // Bağlantıları çiz
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 1;
    connections.forEach(connection => {
        const sourceIcon = icons.find(icon => icon.id === connection.source_id);
        const targetIcon = icons.find(icon => icon.id === connection.target_id);

        if (sourceIcon && targetIcon) {
            const x1 = (sourceIcon.x_position + ICON_SIZE/2) * scaleX;
            const y1 = (sourceIcon.y_position + ICON_SIZE/2) * scaleY;
            const x2 = (targetIcon.x_position + ICON_SIZE/2) * scaleX;
            const y2 = (targetIcon.y_position + ICON_SIZE/2) * scaleY;

            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    });

    updateMiniMapViewport();
}

/**
 * Mini map viewport'unu güncelle
 */
function updateMiniMapViewport() {
    if (!miniMapViewport || !stage) return;

    const stageBox = stage.getClientRect();
    const scale = stage.scaleX();

    const miniMapWidth = miniMapCanvas.clientWidth;
    const miniMapHeight = miniMapCanvas.clientHeight;

    // Viewport boyutları
    const viewportWidth = (stage.width() / scale / 4000) * miniMapWidth;
    const viewportHeight = (stage.height() / scale / 3000) * miniMapHeight;

    // Viewport pozisyonu
    const viewportX = (-stage.x() / scale / 4000) * miniMapWidth;
    const viewportY = (-stage.y() / scale / 3000) * miniMapHeight;

    miniMapViewport.style.left = Math.max(0, Math.min(miniMapWidth - viewportWidth, viewportX)) + 'px';
    miniMapViewport.style.top = Math.max(0, Math.min(miniMapHeight - viewportHeight, viewportY)) + 'px';
    miniMapViewport.style.width = Math.min(miniMapWidth, viewportWidth) + 'px';
    miniMapViewport.style.height = Math.min(miniMapHeight, viewportHeight) + 'px';
}

/**
 * Belirli pozisyona git
 */
function panToPosition(x, y) {
    const targetX = x * 4000; // Canvas genişliği
    const targetY = y * 3000; // Canvas yüksekliği

    // Merkeze hizala
    const newX = stage.width() / 2 - targetX * stage.scaleX();
    const newY = stage.height() / 2 - targetY * stage.scaleY();

    stage.position({ x: newX, y: newY });
    updateMiniMap();
}

/**
 * Butonları başlat
 */
function initButtons() {
    // Eski bağlantı modu butonu artık gerekmiyor
    const connectionBtn = document.getElementById('toggle-connection-mode');
    if (connectionBtn) {
        connectionBtn.style.display = 'none';
    }
}

/**
 * Simge paletini başlat
 */
function initIconPalette() {
    const toggleButton = document.getElementById('toggle-palette');
    const palette = document.getElementById('icon-palette');

    if (toggleButton && palette) {
        toggleButton.addEventListener('click', function() {
            if (palette.style.display === 'none') {
                palette.style.display = 'flex';
            } else {
                palette.style.display = 'none';
            }
        });

        // Simge palette öğelerine event listener ekle
        const paletteItems = palette.querySelectorAll('.icon-palette-item');
        paletteItems.forEach(item => {
            item.addEventListener('click', function() {
                const iconType = this.dataset.type;
                addIconToCanvas(iconType);
            });
        });
    }
}

/**
 * Canvas'a simge ekle
 */
function addIconToCanvas(iconType) {
    if (!canEdit) {
        alert('Bu topolojiyi düzenleme yetkiniz yok.');
        return;
    }

    // Varsayılan isim oluştur
    const defaultName = getDefaultIconName(iconType);

    const iconData = {
        name: defaultName,
        icon_type: iconType,
        x_position: Math.random() * 300 + 50, // Rastgele konum
        y_position: Math.random() * 200 + 50,
        topology_id: topologyId
    };

    fetch('/api/icons', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(iconData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadIcons(); // Simgeleri yeniden yükle
            // Başarılı ise bildirim gösterme
        } else {
            alert('Simge eklenirken hata oluştu: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Simge ekleme hatası:', error);
        alert('Simge eklenirken hata oluştu.');
    });
}

/**
 * Varsayılan simge adını al
 */
function getDefaultIconName(iconType) {
    const names = {
        'server': 'Server',
        'switch': 'Switch',
        'router': 'Router',
        'firewall': 'Firewall',
        'cloud': 'Cloud',
        'database': 'Database',
        'workstation': 'Workstation',
        'printer': 'Printer',
        'wireless': 'Wireless',
        'storage': 'Storage',
        'loadbalancer': 'Load Balancer'
    };
    return names[iconType] || 'Device';
}

// Sayfa yüklendiğinde başlat
document.addEventListener('DOMContentLoaded', function() {
    initKonvaCanvas();

    // Context menu'yu gizlemek için global click event
    document.addEventListener('click', function(e) {
        const contextMenu = document.getElementById('context-menu');
        if (contextMenu && !contextMenu.contains(e.target)) {
            hideContextMenu();
        }
    });

    // Keyboard event'leri
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Context menu'yu gizle
            hideContextMenu();

            // Bağlantı modundan çık
            if (isConnecting) {
                isConnecting = false;
                sourceConnectionPoint = null;
                document.body.style.cursor = 'default';
                console.log('Bağlantı modu iptal edildi');
            }
        } else if (e.key === 'Delete' || e.key === 'Backspace') {
            // Seçili bağlantıyı sil
            if (selectedConnection) {
                deleteConnection(selectedConnection);
            }
        }
    });
});

// ==================== EXPORT/IMPORT/VERSIONING FUNCTIONS ====================

/**
 * Topolojiyi export et
 */
function exportTopology() {
    const topologyId = window.topologyId;

    // Export URL'sine yönlendir
    window.location.href = `/api/topology/${topologyId}/export`;
}

/**
 * Import dialog'unu göster
 */
function showImportDialog() {
    console.log('showImportDialog çağrıldı');

    // Bootstrap modal yerine basit JavaScript modal kullan
    const importModalElement = document.getElementById('importModal');
    console.log('importModalElement:', importModalElement);

    if (importModalElement) {
        console.log('Modal element bulundu, gösteriliyor...');
        importModalElement.style.display = 'block';
        importModalElement.classList.add('show');
        document.body.classList.add('modal-open');

        // Backdrop ekle
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'import-modal-backdrop';
        document.body.appendChild(backdrop);

        // ESC tuşu ile kapatma
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideImportModal();
            }
        });

        // Backdrop tıklama ile kapatma
        backdrop.addEventListener('click', hideImportModal);
    } else {
        console.error('importModal element bulunamadı!');
        alert('Modal element bulunamadı!');
    }
}

/**
 * Import modal'ını kapat
 */
function hideImportModal() {
    const importModalElement = document.getElementById('importModal');
    const backdrop = document.getElementById('import-modal-backdrop');

    if (importModalElement) {
        importModalElement.style.display = 'none';
        importModalElement.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    if (backdrop) {
        backdrop.remove();
    }
}

/**
 * Topolojiyi import et
 */
function importTopology() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('Lütfen bir dosya seçin.');
        return;
    }

    if (!file.name.endsWith('.json')) {
        alert('Lütfen geçerli bir JSON dosyası seçin.');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    const topologyId = window.topologyId;

    fetch(`/api/topology/${topologyId}/import`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Topoloji başarıyla içe aktarıldı!');
            // Modal'ı kapat
            hideImportModal();
            // Sayfayı yenile
            location.reload();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Import hatası:', error);
        alert('İçe aktarma sırasında bir hata oluştu.');
    });
}

/**
 * Versiyon dialog'unu göster
 */
function showVersionDialog() {
    const versionModalElement = document.getElementById('versionModal');
    const versionModal = new bootstrap.Modal(versionModalElement);
    loadVersions();
    versionModal.show();
}

/**
 * Versiyonları yükle
 */
function loadVersions() {
    const topologyId = window.topologyId;

    fetch(`/api/topology/${topologyId}/versions`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayVersions(data.versions, data.current_version);
        } else {
            console.error('Versiyonlar yüklenemedi:', data.error);
        }
    })
    .catch(error => {
        console.error('Versiyon yükleme hatası:', error);
    });
}

/**
 * Versiyonları görüntüle
 */
function displayVersions(versions, currentVersion) {
    const versionList = document.getElementById('versionList');

    if (versions.length === 0) {
        versionList.innerHTML = '<p class="text-muted">Henüz versiyon oluşturulmamış.</p>';
        return;
    }

    let html = '<div class="list-group">';

    versions.forEach(version => {
        const isCurrentVersion = version.version_number === currentVersion;
        const date = new Date(version.created_at).toLocaleString('tr-TR');

        html += `
            <div class="list-group-item ${isCurrentVersion ? 'list-group-item-primary' : ''}">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        Versiyon ${version.version_number}
                        ${isCurrentVersion ? '<span class="badge bg-primary ms-2">Mevcut</span>' : ''}
                    </h6>
                    <small>${date}</small>
                </div>
                <p class="mb-1">${version.description || 'Açıklama yok'}</p>
                ${!isCurrentVersion ? `
                    <button class="btn btn-sm btn-outline-primary" onclick="restoreVersion(${version.id})">
                        <i class="bi bi-arrow-clockwise"></i> Bu Versiyona Dön
                    </button>
                ` : ''}
            </div>
        `;
    });

    html += '</div>';
    versionList.innerHTML = html;
}

/**
 * Yeni versiyon oluşturma dialog'unu göster
 */
function createNewVersion() {
    const createVersionModalElement = document.getElementById('createVersionModal');
    const createVersionModal = new bootstrap.Modal(createVersionModalElement);
    document.getElementById('versionDescription').value = '';
    createVersionModal.show();
}

/**
 * Yeni versiyon kaydet
 */
function saveNewVersion() {
    const description = document.getElementById('versionDescription').value.trim();

    if (!description) {
        alert('Lütfen versiyon açıklaması girin.');
        return;
    }

    const topologyId = window.topologyId;

    fetch(`/api/topology/${topologyId}/versions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Versiyon başarıyla oluşturuldu!');
            // Modal'ları kapat
            const createVersionModalElement = document.getElementById('createVersionModal');
            const createVersionModal = bootstrap.Modal.getInstance(createVersionModalElement);
            if (createVersionModal) {
                createVersionModal.hide();
            }
            // Versiyon listesini güncelle
            loadVersions();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Versiyon oluşturma hatası:', error);
        alert('Versiyon oluşturulurken bir hata oluştu.');
    });
}

/**
 * Versiyonu geri yükle
 */
function restoreVersion(versionId) {
    if (!confirm('Bu versiyona dönmek istediğinizden emin misiniz? Mevcut değişiklikler kaybolacak.')) {
        return;
    }

    const topologyId = window.topologyId;

    fetch(`/api/topology/${topologyId}/versions/${versionId}/restore`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Versiyon başarıyla geri yüklendi!');
            // Modal'ı kapat
            const versionModalElement = document.getElementById('versionModal');
            const versionModal = bootstrap.Modal.getInstance(versionModalElement);
            if (versionModal) {
                versionModal.hide();
            }
            // Sayfayı yenile
            location.reload();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Versiyon geri yükleme hatası:', error);
        alert('Versiyon geri yüklenirken bir hata oluştu.');
    });
}

// ==================== MULTI-SELECT FUNCTIONS ====================

/**
 * Çoklu seçim event'lerini ayarla
 */
function setupMultiSelectEvents() {
    // Keyboard event'leri
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Control' || e.key === 'Meta') {
            isMultiSelectMode = true;
            console.log('Multi-select mode: ON');
        } else if (e.key === ' ' || e.code === 'Space') {
            e.preventDefault(); // Sayfa kaydırmasını engelle
            if (!isPanMode) {
                isPanMode = true;
                stage.draggable(false); // Stage dragging'i kapat
                document.body.style.cursor = 'grab';
                console.log('Pan mode: ON');
            }
        }
    });

    document.addEventListener('keyup', function(e) {
        if (e.key === 'Control' || e.key === 'Meta') {
            isMultiSelectMode = false;
            console.log('Multi-select mode: OFF');
        } else if (e.key === ' ' || e.code === 'Space') {
            isPanMode = false;
            isPanning = false;
            panStart = null;
            stage.draggable(true); // Stage dragging'i geri aç
            document.body.style.cursor = 'default';
            console.log('Pan mode: OFF');
        }
    });

    // Stage mouse events
    stage.on('mousedown', function(e) {
        // Eğer icon üzerinde değilse ve bağlantı modunda değilse
        if (e.target === stage && !isConnecting) {
            if (isPanMode) {
                // Pan modu: Manuel pan başlat
                startManualPan(e);
            } else {
                // Normal mod: Seçim işlemi
                // Stage dragging'i geçici olarak devre dışı bırak
                stage.draggable(false);

                if (!isMultiSelectMode) {
                    // Normal mod: tüm seçimleri temizle
                    clearAllSelections();
                }

                // Seçim dikdörtgeni başlat
                startSelectionRectangle(e);
            }
        }
    });

    stage.on('mousemove', function(e) {
        if (isPanning) {
            updateManualPan(e);
        } else if (isSelecting) {
            updateSelectionRectangle(e);
        }
    });

    stage.on('mouseup', function(e) {
        if (isPanning) {
            finishManualPan();
        } else if (isSelecting) {
            finishSelectionRectangle();
        }

        // Stage dragging'i geri aç (sadece seçim modunda)
        if (!isPanMode) {
            stage.draggable(true);
        }
    });
}

/**
 * Seçim dikdörtgenini başlat
 */
function startSelectionRectangle(e) {
    if (!canEdit) return;

    isSelecting = true;
    const pos = stage.getPointerPosition();
    const transform = stage.getAbsoluteTransform().copy();
    transform.invert();
    const localPos = transform.point(pos);

    selectionStart = localPos;

    // Seçim dikdörtgeni oluştur
    selectionRectangle = new Konva.Rect({
        x: localPos.x,
        y: localPos.y,
        width: 0,
        height: 0,
        fill: 'rgba(0, 123, 255, 0.1)',
        stroke: '#007bff',
        strokeWidth: 1,
        dash: [5, 5]
    });

    layer.add(selectionRectangle);
    layer.draw();
}

/**
 * Seçim dikdörtgenini güncelle
 */
function updateSelectionRectangle(e) {
    if (!selectionRectangle || !selectionStart) return;

    const pos = stage.getPointerPosition();
    const transform = stage.getAbsoluteTransform().copy();
    transform.invert();
    const localPos = transform.point(pos);

    const width = localPos.x - selectionStart.x;
    const height = localPos.y - selectionStart.y;

    selectionRectangle.width(Math.abs(width));
    selectionRectangle.height(Math.abs(height));
    selectionRectangle.x(width < 0 ? localPos.x : selectionStart.x);
    selectionRectangle.y(height < 0 ? localPos.y : selectionStart.y);

    layer.draw();
}

/**
 * Seçim dikdörtgenini bitir
 */
function finishSelectionRectangle() {
    if (!selectionRectangle) return;

    const selectionBox = selectionRectangle.getClientRect();

    // Seçim alanındaki iconları bul
    const iconsInSelection = [];
    layer.find('Group').forEach(group => {
        const iconData = group.getAttr('iconData');
        if (iconData) {
            const iconBox = group.getClientRect();

            // Çakışma kontrolü
            if (boxesIntersect(selectionBox, iconBox)) {
                iconsInSelection.push(iconData);
            }
        }
    });

    // Seçimleri güncelle
    if (isMultiSelectMode) {
        // Ctrl modunda: mevcut seçimlere ekle
        iconsInSelection.forEach(iconData => {
            if (!selectedIcons.find(icon => icon.id === iconData.id)) {
                selectedIcons.push(iconData);
            }
        });
    } else {
        // Normal mod: yeni seçim
        selectedIcons = [...iconsInSelection];
    }

    updateIconSelectionVisuals();

    // Seçim dikdörtgenini temizle
    selectionRectangle.destroy();
    selectionRectangle = null;
    isSelecting = false;
    selectionStart = null;

    // Stage dragging'i geri aç
    stage.draggable(true);

    layer.draw();

    console.log('Seçili iconlar:', selectedIcons.length);
}

/**
 * İki kutunun çakışıp çakışmadığını kontrol et
 */
function boxesIntersect(box1, box2) {
    return !(box1.x + box1.width < box2.x ||
             box2.x + box2.width < box1.x ||
             box1.y + box1.height < box2.y ||
             box2.y + box2.height < box1.y);
}

/**
 * Tüm seçimleri temizle
 */
function clearAllSelections() {
    selectedIcon = null;
    selectedIcons = [];
    updateIconSelectionVisuals();
}

/**
 * Icon seçim görsellerini güncelle
 */
function updateIconSelectionVisuals() {
    // Tüm iconların seçim görsellerini temizle
    layer.find('Group').forEach(group => {
        const iconData = group.getAttr('iconData');
        if (iconData) {
            const background = group.findOne('Rect');
            if (background) {
                background.stroke(null);
                background.strokeWidth(0);
            }
        }
    });

    // Seçili iconları vurgula
    selectedIcons.forEach(iconData => {
        const group = layer.findOne(`#icon-${iconData.id}`);
        if (group) {
            const background = group.findOne('Rect');
            if (background) {
                background.stroke('#007bff');
                background.strokeWidth(3);
            }
        }
    });

    layer.draw();
}

// ==================== ALIGNMENT FUNCTIONS ====================

/**
 * Hizalama event listener'larını ayarla
 */
function setupAlignmentEventListeners() {
    // Yatay hizalama
    const alignLeftBtn = document.getElementById('context-align-left');
    const alignCenterBtn = document.getElementById('context-align-center');
    const alignRightBtn = document.getElementById('context-align-right');

    // Dikey hizalama
    const alignTopBtn = document.getElementById('context-align-top');
    const alignMiddleBtn = document.getElementById('context-align-middle');
    const alignBottomBtn = document.getElementById('context-align-bottom');

    // Dağıtım
    const distributeHorizontalBtn = document.getElementById('context-distribute-horizontal');
    const distributeVerticalBtn = document.getElementById('context-distribute-vertical');

    if (alignLeftBtn) {
        alignLeftBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('left');
        });
    }

    if (alignCenterBtn) {
        alignCenterBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('center');
        });
    }

    if (alignRightBtn) {
        alignRightBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('right');
        });
    }

    if (alignTopBtn) {
        alignTopBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('top');
        });
    }

    if (alignMiddleBtn) {
        alignMiddleBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('middle');
        });
    }

    if (alignBottomBtn) {
        alignBottomBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('bottom');
        });
    }

    if (distributeHorizontalBtn) {
        distributeHorizontalBtn.addEventListener('click', () => {
            hideContextMenu();
            distributeIcons('horizontal');
        });
    }

    if (distributeVerticalBtn) {
        distributeVerticalBtn.addEventListener('click', () => {
            hideContextMenu();
            distributeIcons('vertical');
        });
    }
}

/**
 * İconları hizala
 */
function alignIcons(alignment) {
    if (selectedIcons.length < 2) {
        alert('Hizalama için en az 2 icon seçmelisiniz.');
        return;
    }

    console.log(`Hizalama: ${alignment}, Seçili iconlar: ${selectedIcons.length}`);

    // Referans değerleri hesapla
    let referenceValue;

    switch (alignment) {
        case 'left':
            referenceValue = Math.min(...selectedIcons.map(icon => icon.x_position));
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'center':
            const minX = Math.min(...selectedIcons.map(icon => icon.x_position));
            const maxX = Math.max(...selectedIcons.map(icon => icon.x_position + ICON_SIZE));
            referenceValue = minX + (maxX - minX) / 2 - ICON_SIZE / 2;
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'right':
            referenceValue = Math.max(...selectedIcons.map(icon => icon.x_position + ICON_SIZE)) - ICON_SIZE;
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'top':
            referenceValue = Math.min(...selectedIcons.map(icon => icon.y_position));
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'middle':
            const minY = Math.min(...selectedIcons.map(icon => icon.y_position));
            const maxY = Math.max(...selectedIcons.map(icon => icon.y_position + ICON_SIZE));
            referenceValue = minY + (maxY - minY) / 2 - ICON_SIZE / 2;
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'bottom':
            referenceValue = Math.max(...selectedIcons.map(icon => icon.y_position + ICON_SIZE)) - ICON_SIZE;
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;
    }

    // Görsel güncelleme
    updateIconPositionsVisually();
    displayConnections(); // Bağlantıları yeniden çiz

    console.log(`${alignment} hizalaması tamamlandı`);
}

/**
 * İconları eşit aralıklarla dağıt
 */
function distributeIcons(direction) {
    if (selectedIcons.length < 3) {
        alert('Dağıtım için en az 3 icon seçmelisiniz.');
        return;
    }

    console.log(`Dağıtım: ${direction}, Seçili iconlar: ${selectedIcons.length}`);

    if (direction === 'horizontal') {
        // X koordinatına göre sırala
        const sortedIcons = [...selectedIcons].sort((a, b) => a.x_position - b.x_position);

        const firstX = sortedIcons[0].x_position;
        const lastX = sortedIcons[sortedIcons.length - 1].x_position;
        const totalDistance = lastX - firstX;
        const spacing = totalDistance / (sortedIcons.length - 1);

        sortedIcons.forEach((iconData, index) => {
            if (index > 0 && index < sortedIcons.length - 1) {
                iconData.x_position = firstX + (spacing * index);
                updateIconPosition(iconData);
            }
        });

    } else if (direction === 'vertical') {
        // Y koordinatına göre sırala
        const sortedIcons = [...selectedIcons].sort((a, b) => a.y_position - b.y_position);

        const firstY = sortedIcons[0].y_position;
        const lastY = sortedIcons[sortedIcons.length - 1].y_position;
        const totalDistance = lastY - firstY;
        const spacing = totalDistance / (sortedIcons.length - 1);

        sortedIcons.forEach((iconData, index) => {
            if (index > 0 && index < sortedIcons.length - 1) {
                iconData.y_position = firstY + (spacing * index);
                updateIconPosition(iconData);
            }
        });
    }

    // Görsel güncelleme
    updateIconPositionsVisually();
    displayConnections(); // Bağlantıları yeniden çiz

    console.log(`${direction} dağıtımı tamamlandı`);
}

/**
 * İcon pozisyonlarını görsel olarak güncelle
 */
function updateIconPositionsVisually() {
    selectedIcons.forEach(iconData => {
        const group = layer.findOne(`#icon-${iconData.id}`);
        if (group) {
            group.x(iconData.x_position);
            group.y(iconData.y_position);
        }
    });

    layer.draw();
    updateMiniMap();
}

/**
 * Seçili iconları sil
 */
function deleteSelectedIcons() {
    if (selectedIcons.length === 0) return;

    const iconCount = selectedIcons.length;
    const confirmMessage = `${iconCount} adet seçili simgeyi silmek istediğinizden emin misiniz?`;

    if (!confirm(confirmMessage)) return;

    // Her icon için silme işlemi
    const deletePromises = selectedIcons.map(iconData => {
        return fetch(`/api/icons/${iconData.id}`, {
            method: 'DELETE'
        }).then(response => response.json());
    });

    Promise.all(deletePromises)
        .then(results => {
            const successCount = results.filter(result => result.success).length;
            const failCount = results.length - successCount;

            if (successCount > 0) {
                loadIcons(); // Simgeleri yeniden yükle
                loadConnections(); // Bağlantıları yeniden yükle
                clearAllSelections(); // Seçimleri temizle

                if (failCount === 0) {
                    showNotification(`${successCount} simge başarıyla silindi.`, 'success');
                } else {
                    showNotification(`${successCount} simge silindi, ${failCount} simge silinemedi.`, 'warning');
                }
            } else {
                showNotification('Hiçbir simge silinemedi.', 'error');
            }
        })
        .catch(error => {
            console.error('Toplu silme hatası:', error);
            showNotification('Simgeler silinirken hata oluştu.', 'error');
        });
}

// ==================== ALIGNMENT FUNCTIONS ====================

/**
 * Hizalama event listener'larını ayarla
 */
function setupAlignmentEventListeners() {
    // Yatay hizalama
    const alignLeftBtn = document.getElementById('context-align-left');
    const alignCenterBtn = document.getElementById('context-align-center');
    const alignRightBtn = document.getElementById('context-align-right');

    // Dikey hizalama
    const alignTopBtn = document.getElementById('context-align-top');
    const alignMiddleBtn = document.getElementById('context-align-middle');
    const alignBottomBtn = document.getElementById('context-align-bottom');

    // Dağıtım
    const distributeHorizontalBtn = document.getElementById('context-distribute-horizontal');
    const distributeVerticalBtn = document.getElementById('context-distribute-vertical');

    if (alignLeftBtn) {
        alignLeftBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('left');
        });
    }

    if (alignCenterBtn) {
        alignCenterBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('center');
        });
    }

    if (alignRightBtn) {
        alignRightBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('right');
        });
    }

    if (alignTopBtn) {
        alignTopBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('top');
        });
    }

    if (alignMiddleBtn) {
        alignMiddleBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('middle');
        });
    }

    if (alignBottomBtn) {
        alignBottomBtn.addEventListener('click', () => {
            hideContextMenu();
            alignIcons('bottom');
        });
    }

    if (distributeHorizontalBtn) {
        distributeHorizontalBtn.addEventListener('click', () => {
            hideContextMenu();
            distributeIcons('horizontal');
        });
    }

    if (distributeVerticalBtn) {
        distributeVerticalBtn.addEventListener('click', () => {
            hideContextMenu();
            distributeIcons('vertical');
        });
    }
}

/**
 * İconları hizala
 */
function alignIcons(alignment) {
    if (selectedIcons.length < 2) {
        alert('Hizalama için en az 2 icon seçmelisiniz.');
        return;
    }

    console.log(`Hizalama: ${alignment}, Seçili iconlar: ${selectedIcons.length}`);

    // Referans değerleri hesapla
    let referenceValue;

    switch (alignment) {
        case 'left':
            referenceValue = Math.min(...selectedIcons.map(icon => icon.x_position));
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'center':
            const minX = Math.min(...selectedIcons.map(icon => icon.x_position));
            const maxX = Math.max(...selectedIcons.map(icon => icon.x_position + ICON_SIZE));
            referenceValue = minX + (maxX - minX) / 2 - ICON_SIZE / 2;
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'right':
            referenceValue = Math.max(...selectedIcons.map(icon => icon.x_position + ICON_SIZE)) - ICON_SIZE;
            selectedIcons.forEach(iconData => {
                iconData.x_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'top':
            referenceValue = Math.min(...selectedIcons.map(icon => icon.y_position));
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'middle':
            const minY = Math.min(...selectedIcons.map(icon => icon.y_position));
            const maxY = Math.max(...selectedIcons.map(icon => icon.y_position + ICON_SIZE));
            referenceValue = minY + (maxY - minY) / 2 - ICON_SIZE / 2;
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;

        case 'bottom':
            referenceValue = Math.max(...selectedIcons.map(icon => icon.y_position + ICON_SIZE)) - ICON_SIZE;
            selectedIcons.forEach(iconData => {
                iconData.y_position = referenceValue;
                updateIconPosition(iconData);
            });
            break;
    }

    // Görsel güncelleme
    updateIconPositionsVisually();
    displayConnections(); // Bağlantıları yeniden çiz

    console.log(`${alignment} hizalaması tamamlandı`);
}

/**
 * İconları eşit aralıklarla dağıt
 */
function distributeIcons(direction) {
    if (selectedIcons.length < 3) {
        alert('Dağıtım için en az 3 icon seçmelisiniz.');
        return;
    }

    console.log(`Dağıtım: ${direction}, Seçili iconlar: ${selectedIcons.length}`);

    if (direction === 'horizontal') {
        // X koordinatına göre sırala
        const sortedIcons = [...selectedIcons].sort((a, b) => a.x_position - b.x_position);

        const firstX = sortedIcons[0].x_position;
        const lastX = sortedIcons[sortedIcons.length - 1].x_position;
        const totalDistance = lastX - firstX;
        const spacing = totalDistance / (sortedIcons.length - 1);

        sortedIcons.forEach((iconData, index) => {
            if (index > 0 && index < sortedIcons.length - 1) {
                iconData.x_position = firstX + (spacing * index);
                updateIconPosition(iconData);
            }
        });

    } else if (direction === 'vertical') {
        // Y koordinatına göre sırala
        const sortedIcons = [...selectedIcons].sort((a, b) => a.y_position - b.y_position);

        const firstY = sortedIcons[0].y_position;
        const lastY = sortedIcons[sortedIcons.length - 1].y_position;
        const totalDistance = lastY - firstY;
        const spacing = totalDistance / (sortedIcons.length - 1);

        sortedIcons.forEach((iconData, index) => {
            if (index > 0 && index < sortedIcons.length - 1) {
                iconData.y_position = firstY + (spacing * index);
                updateIconPosition(iconData);
            }
        });
    }

    // Görsel güncelleme
    updateIconPositionsVisually();
    displayConnections(); // Bağlantıları yeniden çiz

    console.log(`${direction} dağıtımı tamamlandı`);
}

/**
 * İcon pozisyonlarını görsel olarak güncelle
 */
function updateIconPositionsVisually() {
    selectedIcons.forEach(iconData => {
        const group = layer.findOne(`#icon-${iconData.id}`);
        if (group) {
            group.x(iconData.x_position);
            group.y(iconData.y_position);
        }
    });

    layer.draw();
    updateMiniMap();
}

/**
 * Seçili iconları sil
 */
function deleteSelectedIcons() {
    if (selectedIcons.length === 0) return;

    const iconCount = selectedIcons.length;
    const confirmMessage = `${iconCount} adet seçili simgeyi silmek istediğinizden emin misiniz?`;

    if (!confirm(confirmMessage)) return;

    // Her icon için silme işlemi
    const deletePromises = selectedIcons.map(iconData => {
        return fetch(`/api/icons/${iconData.id}`, {
            method: 'DELETE'
        }).then(response => response.json());
    });

    Promise.all(deletePromises)
        .then(results => {
            const successCount = results.filter(result => result.success).length;
            const failCount = results.length - successCount;

            if (successCount > 0) {
                loadIcons(); // Simgeleri yeniden yükle
                loadConnections(); // Bağlantıları yeniden yükle
                clearAllSelections(); // Seçimleri temizle

                if (failCount === 0) {
                    showNotification(`${successCount} simge başarıyla silindi.`, 'success');
                } else {
                    showNotification(`${successCount} simge silindi, ${failCount} simge silinemedi.`, 'warning');
                }
            } else {
                showNotification('Hiçbir simge silinemedi.', 'error');
            }
        })
        .catch(error => {
            console.error('Toplu silme hatası:', error);
            showNotification('Simgeler silinirken hata oluştu.', 'error');
        });
}

// ==================== MANUAL PAN FUNCTIONS ====================

/**
 * Manuel pan başlat
 */
function startManualPan(e) {
    if (!isPanMode) return;

    isPanning = true;
    document.body.style.cursor = 'grabbing';

    const pos = stage.getPointerPosition();
    panStart = {
        x: pos.x,
        y: pos.y,
        stageX: stage.x(),
        stageY: stage.y()
    };

    console.log('Manuel pan başladı');
}

/**
 * Manuel pan güncelle
 */
function updateManualPan(e) {
    if (!isPanning || !panStart) return;

    const pos = stage.getPointerPosition();
    const deltaX = pos.x - panStart.x;
    const deltaY = pos.y - panStart.y;

    stage.x(panStart.stageX + deltaX);
    stage.y(panStart.stageY + deltaY);

    stage.batchDraw();
    updateMiniMap();
}

/**
 * Manuel pan bitir
 */
function finishManualPan() {
    if (!isPanning) return;

    isPanning = false;
    panStart = null;

    if (isPanMode) {
        document.body.style.cursor = 'grab';
    } else {
        document.body.style.cursor = 'default';
    }

    console.log('Manuel pan bitti');
}