/**
 * Admin Panel JavaScript
 */

// Global değişkenler
let currentUserId = null;

/**
 * Admin dashboard istatistiklerini yükle
 */
function loadAdminStats() {
    fetch('/admin/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const users = data.users;
                
                // Toplam kullanıcı
                updateStatCard('totalUsers', users.length);
                
                // Aktif kullanıcı
                const activeUsers = users.filter(user => user.is_active).length;
                updateStatCard('activeUsers', activeUsers);
                
                // Admin kullanıcı
                const adminUsers = users.filter(user => user.role === 'admin').length;
                updateStatCard('adminUsers', adminUsers);
                
                // Toplam topoloji
                const totalTopologies = users.reduce((sum, user) => sum + user.topology_count, 0);
                updateStatCard('totalTopologies', totalTopologies);
            } else {
                console.error('İstatistikler yüklenemedi:', data.error);
            }
        })
        .catch(error => {
            console.error('İstatistikler yüklenirken hata:', error);
        });
}

/**
 * İstatistik kartını güncelle
 */
function updateStatCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

/**
 * İstatistikleri yenile
 */
function refreshStats() {
    // Yükleme göstergesi
    const statElements = ['totalUsers', 'activeUsers', 'totalTopologies', 'adminUsers'];
    statElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '...';
        }
    });
    
    // İstatistikleri yenile
    loadAdminStats();
}

/**
 * Kullanıcıları yükle
 */
function loadUsers() {
    const tableContainer = document.getElementById('usersTable');
    if (!tableContainer) return;
    
    // Yükleme göstergesi
    tableContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    `;
    
    fetch('/admin/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.users);
            } else {
                tableContainer.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            }
        })
        .catch(error => {
            console.error('Kullanıcılar yüklenirken hata:', error);
            tableContainer.innerHTML = '<div class="alert alert-danger">Kullanıcılar yüklenirken bir hata oluştu.</div>';
        });
}

/**
 * Kullanıcıları görüntüle
 */
function displayUsers(users) {
    const tableDiv = document.getElementById('usersTable');
    
    if (users.length === 0) {
        tableDiv.innerHTML = '<div class="alert alert-info">Henüz kullanıcı bulunmuyor.</div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
    html += '<thead class="table-dark"><tr>';
    html += '<th>ID</th><th>Kullanıcı Adı</th><th>Email</th><th>Rol</th><th>Durum</th><th>Topoloji</th><th>Kayıt Tarihi</th><th>İşlemler</th>';
    html += '</tr></thead><tbody>';
    
    users.forEach(user => {
        const statusBadge = user.is_active 
            ? '<span class="badge bg-success">Aktif</span>'
            : '<span class="badge bg-danger">Pasif</span>';
        
        const roleBadge = user.role === 'admin'
            ? '<span class="badge bg-primary">Admin</span>'
            : '<span class="badge bg-secondary">Kullanıcı</span>';
        
        const registerDate = new Date(user.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
        
        html += `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${roleBadge}</td>
                <td>${statusBadge}</td>
                <td>${user.topology_count}</td>
                <td>${registerDate}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-warning" onclick="resetPassword(${user.id}, '${user.username}')" title="Şifre Sıfırla">
                            <i class="bi bi-key"></i>
                        </button>
                        <button type="button" class="btn btn-outline-${user.is_active ? 'secondary' : 'success'}" onclick="toggleUserStatus(${user.id}, '${user.username}', ${user.is_active})" title="${user.is_active ? 'Pasif Yap' : 'Aktif Yap'}">
                            <i class="bi bi-${user.is_active ? 'pause' : 'play'}"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="changeRole(${user.id}, '${user.username}', '${user.role}')" title="Rol Değiştir">
                            <i class="bi bi-shield"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.username}')" title="Kullanıcı Sil">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    tableDiv.innerHTML = html;
}

/**
 * Kullanıcı durumunu değiştir
 */
function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? 'pasif' : 'aktif';
    
    if (!confirm(`${username} kullanıcısını ${action} yapmak istediğinizden emin misiniz?`)) {
        return;
    }
    
    fetch(`/admin/api/users/${userId}/toggle-status`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUsers();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Durum değiştirme hatası:', error);
        showAlert('danger', 'Durum değiştirme sırasında bir hata oluştu.');
    });
}

/**
 * Şifre sıfırlama
 */
function resetPassword(userId, username) {
    currentUserId = userId;
    const modal = document.getElementById('resetPasswordModal');
    if (modal) {
        document.getElementById('resetPasswordUserInfo').textContent = username;
        new bootstrap.Modal(modal).show();
    }
}

/**
 * Rol değiştirme
 */
function changeRole(userId, username, currentRole) {
    const newRole = currentRole === 'admin' ? 'user' : 'admin';
    
    if (!confirm(`${username} kullanıcısının rolünü ${newRole} yapmak istediğinizden emin misiniz?`)) {
        return;
    }
    
    fetch(`/admin/api/users/${userId}/change-role`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            role: newRole
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUsers();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Rol değiştirme hatası:', error);
        showAlert('danger', 'Rol değiştirme sırasında bir hata oluştu.');
    });
}

/**
 * Kullanıcı silme
 */
function deleteUser(userId, username) {
    currentUserId = userId;
    const modal = document.getElementById('deleteUserModal');
    if (modal) {
        document.getElementById('deleteUserInfo').textContent = username;
        new bootstrap.Modal(modal).show();
    }
}

/**
 * Şifre kopyalama
 */
function copyPassword() {
    const passwordField = document.getElementById('newPasswordDisplay');
    if (passwordField) {
        passwordField.select();
        passwordField.setSelectionRange(0, 99999);
        navigator.clipboard.writeText(passwordField.value);
        
        // Kopyalama feedback'i
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Kopyalandı';
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }
}

/**
 * Alert göster
 */
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Sayfanın üstüne ekle
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
    
    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Event listener'ları ekle
document.addEventListener('DOMContentLoaded', function() {
    // Admin dashboard sayfasında istatistikleri yükle
    if (document.getElementById('totalUsers')) {
        loadAdminStats();
    }
    
    // Kullanıcı yönetimi sayfasında kullanıcıları yükle
    if (document.getElementById('usersTable')) {
        loadUsers();
    }
    
    // Modal event listener'ları
    const confirmResetBtn = document.getElementById('confirmResetPasswordBtn');
    if (confirmResetBtn) {
        confirmResetBtn.addEventListener('click', function() {
            fetch(`/admin/api/users/${currentUserId}/reset-password`, {
                method: 'PUT'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Şifre sıfırlama modal'ını kapat
                    const resetModal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
                    if (resetModal) resetModal.hide();
                    
                    // Yeni şifreyi göster
                    document.getElementById('newPasswordDisplay').value = data.new_password;
                    new bootstrap.Modal(document.getElementById('newPasswordModal')).show();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                console.error('Şifre sıfırlama hatası:', error);
                showAlert('danger', 'Şifre sıfırlama sırasında bir hata oluştu.');
            });
        });
    }
    
    const confirmDeleteBtn = document.getElementById('confirmDeleteUserBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            fetch(`/admin/api/users/${currentUserId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    loadUsers();
                } else {
                    showAlert('danger', data.error);
                }
                
                // Modal'ı kapat
                const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteUserModal'));
                if (deleteModal) deleteModal.hide();
            })
            .catch(error => {
                console.error('Kullanıcı silme hatası:', error);
                showAlert('danger', 'Kullanıcı silme sırasında bir hata oluştu.');
            });
        });
    }
});
