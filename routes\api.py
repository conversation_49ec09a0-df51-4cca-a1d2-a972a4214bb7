"""
API route'ları
"""
import os
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, url_for, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import db, User, Topology, TopologyShare, Icon, Connection, Note, TopologyVersion
from utils import allowed_file, delete_topology_recursive, generate_random_password

api_bp = Blueprint('api', __name__)

# Kullanıcı Yönetimi API'leri

@api_bp.route('/users/search', methods=['GET'])
@login_required
def search_users():
    """Kullanıcı arama - email veya username ile"""
    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify({'success': False, 'error': 'En az 2 karakter girmelisiniz.'})

    # Mevcut kullanıcıyı hariç tut
    users = User.query.filter(
        User.id != current_user.id,
        User.is_active_user == True,
        db.or_(
            User.username.ilike(f'%{query}%'),
            User.email.ilike(f'%{query}%')
        )
    ).limit(10).all()

    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email
        })

    return jsonify({'success': True, 'users': users_data})

# Topoloji Paylaşım API'leri

@api_bp.route('/topology/<int:topology_id>/share', methods=['POST'])
@login_required
def share_topology(topology_id):
    """Topolojiyi kullanıcıyla paylaş"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip paylaşabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi paylaşma yetkiniz yok.'}), 403

    data = request.json
    user_id = data.get('user_id')
    permission = data.get('permission', 'readonly')

    if permission not in ['readonly', 'edit']:
        return jsonify({'success': False, 'error': 'Geçersiz izin türü.'}), 400

    # Kullanıcının varlığını kontrol et
    target_user = User.query.get(user_id)
    if not target_user:
        return jsonify({'success': False, 'error': 'Kullanıcı bulunamadı.'}), 404

    # Kendisiyle paylaşmaya çalışıyor mu?
    if user_id == current_user.id:
        return jsonify({'success': False, 'error': 'Topolojiyi kendinizle paylaşamazsınız.'}), 400

    try:
        # Mevcut paylaşımı kontrol et
        existing_share = TopologyShare.query.filter_by(
            topology_id=topology_id,
            user_id=user_id
        ).first()

        if existing_share:
            # Mevcut paylaşımı güncelle
            existing_share.permission = permission
            existing_share.created_at = datetime.utcnow()
        else:
            # Yeni paylaşım oluştur
            share = TopologyShare(
                topology_id=topology_id,
                user_id=user_id,
                permission=permission,
                shared_by=current_user.id
            )
            db.session.add(share)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Topoloji {target_user.username} ile {permission} izniyle paylaşıldı.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Paylaşım sırasında bir hata oluştu.'}), 500

@api_bp.route('/topology/<int:topology_id>/shares', methods=['GET'])
@login_required
def get_topology_shares(topology_id):
    """Topolojinin paylaşım listesini getir"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip görebilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojinin paylaşımlarını görme yetkiniz yok.'}), 403

    shares = TopologyShare.query.filter_by(topology_id=topology_id).all()

    shares_data = []
    for share in shares:
        shares_data.append({
            'id': share.id,
            'user_id': share.user_id,
            'username': share.user.username,
            'email': share.user.email,
            'permission': share.permission,
            'created_at': share.created_at.isoformat()
        })

    return jsonify({'success': True, 'shares': shares_data})

@api_bp.route('/topology/<int:topology_id>/share/<int:share_id>', methods=['DELETE'])
@login_required
def remove_topology_share(topology_id, share_id):
    """Topoloji paylaşımını kaldır"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip kaldırabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu paylaşımı kaldırma yetkiniz yok.'}), 403

    share = TopologyShare.query.get_or_404(share_id)

    # Paylaşımın bu topolojiye ait olduğunu kontrol et
    if share.topology_id != topology_id:
        return jsonify({'success': False, 'error': 'Geçersiz paylaşım.'}), 400

    try:
        db.session.delete(share)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Paylaşım kaldırıldı.'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Paylaşım kaldırılırken bir hata oluştu.'}), 500

# Topoloji Yönetimi API'leri

@api_bp.route('/topology/<int:topology_id>/rename', methods=['PUT'])
@login_required
def rename_topology(topology_id):
    """Topoloji adını değiştir"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip yeniden adlandırabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi yeniden adlandırma yetkiniz yok.'}), 403

    data = request.json
    new_name = data.get('name', '').strip()

    if not new_name:
        return jsonify({'success': False, 'error': 'Geçerli bir isim belirtmelisiniz.'}), 400

    if len(new_name) > 100:
        return jsonify({'success': False, 'error': 'İsim 100 karakterden uzun olamaz.'}), 400

    try:
        topology.name = new_name
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Topoloji adı başarıyla değiştirildi.',
            'new_name': new_name
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Ad değiştirme sırasında bir hata oluştu.'}), 500

@api_bp.route('/topology/<int:topology_id>', methods=['DELETE'])
@login_required
def delete_topology_api(topology_id):
    """Topolojiyi sil"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip silebilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi silme yetkiniz yok.'}), 403

    try:
        # Özyinelemeli olarak topoloji ve tüm alt öğelerini sil
        delete_topology_recursive(topology)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Topoloji başarıyla silindi.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Topoloji silinirken bir hata oluştu.'}), 500

# Simge API'leri

@api_bp.route('/topology/<int:topology_id>/icons', methods=['GET'])
@login_required
def get_topology_icons(topology_id):
    """Topoloji simgelerini getir"""
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    icons = Icon.query.filter_by(topology_id=topology_id).all()

    return jsonify([{
        'id': icon.id,
        'name': icon.name,
        'icon_type': icon.icon_type,
        'x_position': icon.x_position,
        'y_position': icon.y_position
    } for icon in icons])

@api_bp.route('/icons', methods=['POST'])
@login_required
def add_icon():
    """Simge ekle"""
    data = request.json

    # Gerekli alanları kontrol et
    required_fields = ['name', 'icon_type', 'x_position', 'y_position', 'topology_id']
    if not all(field in data for field in required_fields):
        return jsonify({'success': False, 'error': 'Eksik parametreler.'}), 400

    # Topoloji erişim kontrolü - Düzenleme izni gerekli
    topology = Topology.query.get_or_404(data['topology_id'])
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Simge oluştur
    icon = Icon(
        name=data['name'],
        icon_type=data['icon_type'],
        x_position=data['x_position'],
        y_position=data['y_position'],
        topology_id=data['topology_id']
    )

    db.session.add(icon)
    db.session.commit()

    return jsonify({
        'success': True,
        'icon': {
            'id': icon.id,
            'name': icon.name,
            'icon_type': icon.icon_type,
            'x_position': icon.x_position,
            'y_position': icon.y_position
        }
    })

@api_bp.route('/icons/<int:icon_id>', methods=['PUT'])
@login_required
def update_icon(icon_id):
    """Simge güncelle"""
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgeyi düzenleme izniniz yok.'}), 403

    data = request.json

    # Güncelleme alanları
    if 'name' in data:
        icon.name = data['name']
    if 'x_position' in data:
        icon.x_position = data['x_position']
    if 'y_position' in data:
        icon.y_position = data['y_position']

    db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/icons/<int:icon_id>', methods=['DELETE'])
@login_required
def delete_icon(icon_id):
    """Simge sil"""
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgeyi silme izniniz yok.'}), 403

    # Alt topoloji varsa sil
    if icon.sub_topology:
        delete_topology_recursive(icon.sub_topology)

    # Simgenin notlarını sil
    Note.query.filter_by(icon_id=icon_id).delete()

    # Simgenin bağlantılarını sil
    Connection.query.filter(
        (Connection.source_id == icon_id) |
        (Connection.target_id == icon_id)
    ).delete()

    # Simgeyi sil
    db.session.delete(icon)
    db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/icons/<int:icon_id>/sub_topology', methods=['GET'])
@login_required
def get_icon_sub_topology(icon_id):
    """Simgenin alt topolojisini kontrol et"""
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Erişim kontrolü
    if not topology.can_user_access(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    return jsonify({
        'success': True,
        'sub_topology_id': icon.sub_topology_id,
        'has_sub_topology': icon.sub_topology_id is not None
    })

# Bağlantı API'leri

@api_bp.route('/topology/<int:topology_id>/connections', methods=['GET'])
@login_required
def get_connections(topology_id):
    """Topoloji bağlantılarını getir"""
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    connections = Connection.query.filter_by(topology_id=topology_id).all()

    return jsonify([{
        'id': conn.id,
        'name': conn.name,
        'source_id': conn.source_id,
        'target_id': conn.target_id,
        'source_position': conn.source_position,
        'target_position': conn.target_position,
        'source_name': conn.source.name,
        'target_name': conn.target.name
    } for conn in connections])

@api_bp.route('/connections', methods=['POST'])
@login_required
def add_connection():
    """Bağlantı ekle"""
    data = request.json

    # Gerekli alanları kontrol et
    if not all(key in data for key in ['source_id', 'target_id', 'topology_id']):
        return jsonify({'success': False, 'error': 'Eksik parametreler.'}), 400

    # Topoloji erişim kontrolü - Düzenleme izni gerekli
    topology = Topology.query.get_or_404(data['topology_id'])
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Kaynak ve hedef simgelerin varlığını kontrol et
    source = Icon.query.get_or_404(data['source_id'])
    target = Icon.query.get_or_404(data['target_id'])

    # Simgelerin aynı topolojide olduğunu kontrol et
    if source.topology_id != data['topology_id'] or target.topology_id != data['topology_id']:
        return jsonify({'success': False, 'error': 'Simgeler aynı topolojide olmalıdır.'}), 400

    # Bağlantı oluştur
    connection = Connection(
        name=data.get('name', ''),
        source_id=data['source_id'],
        target_id=data['target_id'],
        source_position=data.get('source_position', 'right'),
        target_position=data.get('target_position', 'left'),
        topology_id=data['topology_id']
    )

    db.session.add(connection)
    db.session.commit()

    return jsonify({
        'success': True,
        'connection': {
            'id': connection.id,
            'name': connection.name,
            'source_id': connection.source_id,
            'target_id': connection.target_id
        }
    })

@api_bp.route('/connections/<int:connection_id>', methods=['PUT'])
@login_required
def update_connection(connection_id):
    """Bağlantı güncelle"""
    connection = Connection.query.get_or_404(connection_id)
    topology = Topology.query.get_or_404(connection.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu bağlantıyı düzenleme izniniz yok.'}), 403

    data = request.json

    # Bağlantı adını güncelle
    if 'name' in data:
        connection.name = data['name']

    db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/connections/<int:connection_id>', methods=['DELETE'])
@login_required
def delete_connection(connection_id):
    """Bağlantı sil"""
    connection = Connection.query.get_or_404(connection_id)
    topology = Topology.query.get_or_404(connection.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu bağlantıyı silme izniniz yok.'}), 403

    db.session.delete(connection)
    db.session.commit()

    return jsonify({'success': True})

# Not API'leri

@api_bp.route('/notes/<int:icon_id>', methods=['GET'])
@login_required
def get_notes(icon_id):
    """Simge notlarını getir"""
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Erişim kontrolü
    if not topology.can_user_access(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    notes = Note.query.filter_by(icon_id=icon_id).order_by(Note.created_at.desc()).all()

    notes_data = []
    for note in notes:
        notes_data.append({
            'id': note.id,
            'content': note.content,
            'created_at': note.created_at.isoformat(),
            'updated_at': note.updated_at.isoformat()
        })

    return jsonify({'success': True, 'notes': notes_data})

@api_bp.route('/notes', methods=['POST'])
@login_required
def add_note():
    """Not ekle"""
    data = request.json

    if not all(key in data for key in ['icon_id', 'content']):
        return jsonify({'success': False, 'error': 'Eksik parametreler.'}), 400

    icon = Icon.query.get_or_404(data['icon_id'])
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgeye not ekleme izniniz yok.'}), 403

    # Not oluştur
    note = Note(
        content=data['content'],
        icon_id=data['icon_id']
    )

    db.session.add(note)
    db.session.commit()

    return jsonify({
        'success': True,
        'note': {
            'id': note.id,
            'content': note.content,
            'created_at': note.created_at.isoformat(),
            'updated_at': note.updated_at.isoformat()
        }
    })

@api_bp.route('/notes/<int:note_id>', methods=['PUT'])
@login_required
def update_note(note_id):
    """Not güncelle"""
    note = Note.query.get_or_404(note_id)
    icon = Icon.query.get_or_404(note.icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu notu düzenleme izniniz yok.'}), 403

    data = request.json

    if 'content' in data:
        note.content = data['content']
        note.updated_at = datetime.utcnow()

    db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/notes/<int:note_id>', methods=['DELETE'])
@login_required
def delete_note(note_id):
    """Not sil"""
    note = Note.query.get_or_404(note_id)
    icon = Icon.query.get_or_404(note.icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu notu silme izniniz yok.'}), 403

    db.session.delete(note)
    db.session.commit()

    return jsonify({'success': True})

# Arkaplan Resmi API'leri

@api_bp.route('/topology/<int:topology_id>/background', methods=['GET'])
@login_required
def get_background(topology_id):
    """Arkaplan resmi bilgisini getir"""
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    if topology.background_image:
        # Dosyanın var olup olmadığını kontrol et
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], topology.background_image)
        if not os.path.exists(file_path):
            print(f"Arkaplan resmi dosyası bulunamadı: {file_path}")
            topology.background_image = None
            db.session.commit()
            return jsonify({
                'success': True,
                'has_background': False
            })

        background_url = url_for('static', filename=f'uploads/{topology.background_image}')
        print(f"Arkaplan resmi URL'si: {background_url}")

        return jsonify({
            'success': True,
            'has_background': True,
            'background_image': background_url,
            'background_x': topology.background_x,
            'background_y': topology.background_y,
            'background_scale': topology.background_scale
        })
    else:
        return jsonify({
            'success': True,
            'has_background': False
        })

# Export/Import/Versioning API'leri

@api_bp.route('/topology/<int:topology_id>/export', methods=['GET'])
@login_required
def export_topology(topology_id):
    """Topolojiyi JSON olarak export et"""
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    # Topoloji temel bilgilerini al
    topology_data = {
        'id': topology.id,
        'name': topology.name,
        'created_at': topology.created_at.isoformat(),
        'background_image': topology.background_image,
        'background_x': topology.background_x,
        'background_y': topology.background_y,
        'background_scale': topology.background_scale,
        'current_version': topology.current_version
    }

    # Simgeleri al
    icons = Icon.query.filter_by(topology_id=topology_id).all()
    icons_data = []

    for icon in icons:
        icon_data = {
            'id': icon.id,
            'name': icon.name,
            'icon_type': icon.icon_type,
            'x_position': icon.x_position,
            'y_position': icon.y_position,
            'sub_topology_id': icon.sub_topology_id
        }
        icons_data.append(icon_data)

    # Bağlantıları al
    connections = Connection.query.filter_by(topology_id=topology_id).all()
    connections_data = []

    for connection in connections:
        connection_data = {
            'id': connection.id,
            'name': connection.name,
            'source_id': connection.source_id,
            'target_id': connection.target_id,
            'source_position': connection.source_position,
            'target_position': connection.target_position
        }
        connections_data.append(connection_data)

    # Tüm verileri birleştir
    export_data = {
        'topology': topology_data,
        'icons': icons_data,
        'connections': connections_data,
        'export_date': datetime.utcnow().isoformat(),
        'export_version': '1.0'
    }

    # JSON dosyası olarak indir
    response = jsonify(export_data)
    response.headers['Content-Disposition'] = f'attachment; filename=topology_{topology_id}_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}.json'
    return response

@api_bp.route('/topology/<int:topology_id>/import', methods=['POST'])
@login_required
def import_topology(topology_id):
    """Topolojiyi JSON'dan import et"""
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    # Dosya kontrolü
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'Dosya bulunamadı.'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'success': False, 'error': 'Dosya seçilmedi.'}), 400

    if not file.filename.endswith('.json'):
        return jsonify({'success': False, 'error': 'Geçersiz dosya formatı. Sadece JSON dosyaları kabul edilir.'}), 400

    try:
        # JSON dosyasını oku
        import_data = json.loads(file.read().decode('utf-8'))

        # Versiyon oluştur (önce mevcut durumu kaydet)
        try:
            create_topology_version(topology_id, f"İçe aktarma öncesi: {file.filename}")
        except Exception as ve:
            print(f"Versiyon oluşturulurken hata: {str(ve)}")
            # Versiyon oluşturulamazsa devam et

        # Mevcut simgeleri ve bağlantıları temizle
        Icon.query.filter_by(topology_id=topology_id).delete()
        Connection.query.filter_by(topology_id=topology_id).delete()

        # Topoloji bilgilerini güncelle
        topology_data = import_data.get('topology', {})
        topology.name = topology_data.get('name', topology.name)
        topology.background_image = topology_data.get('background_image', topology.background_image)
        topology.background_x = topology_data.get('background_x', topology.background_x)
        topology.background_y = topology_data.get('background_y', topology.background_y)
        topology.background_scale = topology_data.get('background_scale', topology.background_scale)

        # Simgeleri içe aktar
        icon_id_map = {}  # Eski ID -> Yeni ID eşleştirmesi
        for icon_data in import_data.get('icons', []):
            old_id = icon_data.get('id')
            icon = Icon(
                name=icon_data.get('name', ''),
                icon_type=icon_data.get('icon_type', 'server'),
                x_position=icon_data.get('x_position', 0),
                y_position=icon_data.get('y_position', 0),
                topology_id=topology_id
            )
            db.session.add(icon)
            db.session.flush()  # ID'yi al
            icon_id_map[old_id] = icon.id

        # Bağlantıları içe aktar
        for conn_data in import_data.get('connections', []):
            # Eski ID'leri yeni ID'lere dönüştür
            old_source_id = conn_data.get('source_id')
            old_target_id = conn_data.get('target_id')

            if old_source_id in icon_id_map and old_target_id in icon_id_map:
                connection = Connection(
                    name=conn_data.get('name', ''),
                    source_id=icon_id_map[old_source_id],
                    target_id=icon_id_map[old_target_id],
                    source_position=conn_data.get('source_position', 'right'),
                    target_position=conn_data.get('target_position', 'left'),
                    topology_id=topology_id
                )
                db.session.add(connection)

        db.session.commit()

        return jsonify({'success': True, 'message': 'Topoloji başarıyla içe aktarıldı.'})

    except Exception as e:
        print(f"JSON import sırasında hata: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'İçe aktarma sırasında bir hata oluştu: {str(e)}'}), 500

# Versiyonlama API'leri

@api_bp.route('/topology/<int:topology_id>/versions', methods=['GET'])
@login_required
def get_versions(topology_id):
    """Topoloji versiyonlarını listele"""
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    try:
        versions = TopologyVersion.query.filter_by(topology_id=topology_id).order_by(TopologyVersion.version_number.desc()).all()

        return jsonify({
            'success': True,
            'current_version': topology.current_version,
            'versions': [{
                'id': version.id,
                'version_number': version.version_number,
                'description': version.description,
                'created_at': version.created_at.isoformat()
            } for version in versions]
        })
    except Exception as e:
        print(f"Versiyonlar listelenirken hata oluştu: {str(e)}")
        return jsonify({'success': False, 'error': f'Versiyonlar listelenirken bir hata oluştu: {str(e)}'}), 500

@api_bp.route('/topology/<int:topology_id>/versions', methods=['POST'])
@login_required
def create_version(topology_id):
    """Yeni versiyon oluştur"""
    try:
        topology = Topology.query.get_or_404(topology_id)

        if topology.user_id != current_user.id:
            return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

        data = request.json or {}
        description = data.get('description', f'Manuel versiyon {datetime.now().strftime("%Y-%m-%d %H:%M")}')

        print(f"Versiyon oluşturuluyor: {description}")

        try:
            version = create_topology_version(topology_id, description)

            return jsonify({
                'success': True,
                'version': {
                    'id': version.id,
                    'version_number': version.version_number,
                    'description': version.description,
                    'created_at': version.created_at.isoformat()
                }
            })
        except Exception as e:
            print(f"Versiyon oluşturulurken iç hata: {str(e)}")
            return jsonify({'success': False, 'error': f'Versiyon oluşturulurken bir hata oluştu: {str(e)}'}), 500
    except Exception as e:
        print(f"Versiyon oluşturulurken genel hata: {str(e)}")
        return jsonify({'success': False, 'error': f'Versiyon oluşturulurken bir hata oluştu: {str(e)}'}), 500

@api_bp.route('/topology/<int:topology_id>/versions/<int:version_id>/restore', methods=['POST'])
@login_required
def restore_version(topology_id, version_id):
    """Versiyonu geri yükle"""
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    version = TopologyVersion.query.get_or_404(version_id)

    if version.topology_id != topology_id:
        return jsonify({'success': False, 'error': 'Geçersiz versiyon.'}), 400

    try:
        # Mevcut durumu yeni versiyon olarak kaydet
        try:
            create_topology_version(topology_id, f"Versiyon {version.version_number} geri yüklemesi öncesi")
        except Exception as ve:
            print(f"Versiyon oluşturulurken hata: {str(ve)}")

        # Versiyon verisini oku
        import_data = json.loads(version.data)

        # Mevcut simgeleri ve bağlantıları temizle
        Icon.query.filter_by(topology_id=topology_id).delete()
        Connection.query.filter_by(topology_id=topology_id).delete()

        # Topoloji bilgilerini güncelle
        topology_data = import_data.get('topology', {})
        topology.name = topology_data.get('name', topology.name)
        topology.background_image = topology_data.get('background_image', topology.background_image)
        topology.background_x = topology_data.get('background_x', topology.background_x)
        topology.background_y = topology_data.get('background_y', topology.background_y)
        topology.background_scale = topology_data.get('background_scale', topology.background_scale)

        # Simgeleri içe aktar
        icon_id_map = {}  # Eski ID -> Yeni ID eşleştirmesi
        for icon_data in import_data.get('icons', []):
            old_id = icon_data.get('id')
            icon = Icon(
                name=icon_data.get('name', ''),
                icon_type=icon_data.get('icon_type', 'server'),
                x_position=icon_data.get('x_position', 0),
                y_position=icon_data.get('y_position', 0),
                topology_id=topology_id
            )
            db.session.add(icon)
            db.session.flush()  # ID'yi al
            icon_id_map[old_id] = icon.id

        # Bağlantıları içe aktar
        for conn_data in import_data.get('connections', []):
            # Eski ID'leri yeni ID'lere dönüştür
            old_source_id = conn_data.get('source_id')
            old_target_id = conn_data.get('target_id')

            if old_source_id in icon_id_map and old_target_id in icon_id_map:
                connection = Connection(
                    name=conn_data.get('name', ''),
                    source_id=icon_id_map[old_source_id],
                    target_id=icon_id_map[old_target_id],
                    source_position=conn_data.get('source_position', 'right'),
                    target_position=conn_data.get('target_position', 'left'),
                    topology_id=topology_id
                )
                db.session.add(connection)

        db.session.commit()

        return jsonify({'success': True, 'message': f'Topoloji başarıyla versiyon {version.version_number}\'e geri döndürüldü.'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'Versiyon geri yüklenirken bir hata oluştu: {str(e)}'}), 500

# Yardımcı fonksiyonlar

def create_topology_version(topology_id, description, data=None):
    """Topoloji versiyonu oluştur"""
    try:
        topology = Topology.query.get_or_404(topology_id)

        if data is None:
            # Topoloji verilerini al
            export_data = get_topology_export_data(topology_id)
            data = json.dumps(export_data)

        # Yeni versiyon numarası
        if topology.current_version is None:
            topology.current_version = 0

        version_number = topology.current_version + 1

        # Versiyon oluştur
        version = TopologyVersion(
            topology_id=topology_id,
            version_number=version_number,
            description=description,
            data=data
        )

        # Topolojinin mevcut versiyon numarasını güncelle
        topology.current_version = version_number

        db.session.add(version)
        db.session.commit()

        return version
    except Exception as e:
        print(f"Versiyon oluşturulurken hata: {str(e)}")
        db.session.rollback()
        raise e

def get_topology_export_data(topology_id):
    """Topoloji export verisini al"""
    try:
        topology = Topology.query.get_or_404(topology_id)

        # Topoloji temel bilgilerini al
        topology_data = {
            'id': topology.id,
            'name': topology.name,
            'created_at': topology.created_at.isoformat(),
            'background_image': topology.background_image,
            'background_x': topology.background_x,
            'background_y': topology.background_y,
            'background_scale': topology.background_scale,
            'current_version': topology.current_version
        }

        # Simgeleri al
        icons = Icon.query.filter_by(topology_id=topology_id).all()
        icons_data = []

        for icon in icons:
            icon_data = {
                'id': icon.id,
                'name': icon.name,
                'icon_type': icon.icon_type,
                'x_position': icon.x_position,
                'y_position': icon.y_position,
                'sub_topology_id': icon.sub_topology_id
            }
            icons_data.append(icon_data)

        # Bağlantıları al
        connections = Connection.query.filter_by(topology_id=topology_id).all()
        connections_data = []

        for connection in connections:
            connection_data = {
                'id': connection.id,
                'name': connection.name,
                'source_id': connection.source_id,
                'target_id': connection.target_id,
                'source_position': connection.source_position,
                'target_position': connection.target_position
            }
            connections_data.append(connection_data)

        # Tüm verileri birleştir
        export_data = {
            'topology': topology_data,
            'icons': icons_data,
            'connections': connections_data,
            'export_date': datetime.now().isoformat(),
            'export_version': '1.0'
        }

        return export_data
    except Exception as e:
        print(f"Topoloji verileri alınırken hata: {str(e)}")
        raise e

@api_bp.route('/topology/<int:topology_id>/background', methods=['POST'])
@login_required
def upload_background(topology_id):
    """Arkaplan resmi yükle"""
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Dosya kontrolü
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'Dosya bulunamadı.'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'success': False, 'error': 'Dosya seçilmedi.'}), 400

    if not allowed_file(file.filename, current_app.config['ALLOWED_EXTENSIONS']):
        return jsonify({'success': False, 'error': 'Geçersiz dosya formatı. İzin verilen formatlar: png, jpg, jpeg, gif, svg'}), 400

    try:
        # Güvenli dosya adı oluştur
        filename = secure_filename(file.filename)

        # Benzersiz dosya adı oluştur
        import uuid
        unique_filename = f"{uuid.uuid4().hex}_{filename}"

        # Dosyayı kaydet
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        # Eski arkaplan resmini sil
        if topology.background_image:
            old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], topology.background_image)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)

        # Veritabanını güncelle
        topology.background_image = unique_filename
        db.session.commit()

        # Tam URL oluştur
        background_url = url_for('static', filename=f'uploads/{unique_filename}')

        print(f"Arkaplan resmi yüklendi: {background_url}")

        return jsonify({
            'success': True,
            'background_image': background_url
        })

    except Exception as e:
        print(f"Arkaplan resmi yükleme hatası: {str(e)}")
        return jsonify({'success': False, 'error': 'Dosya yükleme sırasında bir hata oluştu.'}), 500

@api_bp.route('/topology/<int:topology_id>/background', methods=['DELETE'])
@login_required
def remove_background(topology_id):
    """Arkaplan resmini kaldır"""
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Arkaplan resmi varsa sil
    if topology.background_image:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], topology.background_image)
        if os.path.exists(file_path):
            os.remove(file_path)

        topology.background_image = None
        db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/topology/<int:topology_id>/background/position', methods=['PUT'])
@login_required
def update_background_position(topology_id):
    """Arkaplan resmi konumunu güncelle"""
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    if not topology.background_image:
        return jsonify({'success': False, 'error': 'Bu topolojide arkaplan resmi bulunmuyor.'}), 400

    data = request.json

    # X, Y ve ölçek değerlerini güncelle
    if 'x' in data:
        topology.background_x = float(data['x'])
    if 'y' in data:
        topology.background_y = float(data['y'])
    if 'scale' in data:
        topology.background_scale = float(data['scale'])

    db.session.commit()

    return jsonify({'success': True})