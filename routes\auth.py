"""
Kimlik doğrulama route'ları
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
from models import db, User

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/')
def index():
    """Ana sayfa"""
    if current_user.is_authenticated:
        return redirect(url_for('topology.dashboard'))
    return render_template('auth/login.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """<PERSON><PERSON><PERSON> sayfası"""
    if current_user.is_authenticated:
        return redirect(url_for('topology.dashboard'))
    
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if not user.is_active():
                flash('He<PERSON>bınız devre dışı bırakılmış. Lütfen yönetici ile iletişime geçin.')
                return render_template('auth/login.html')
            
            login_user(user)
            
            # Admin kullanıcıyı admin dashboard'a yönlendir
            if user.is_admin():
                return redirect(url_for('admin.dashboard'))
            else:
                return redirect(url_for('topology.dashboard'))
        else:
            flash('Geçersiz kullanıcı adı veya şifre.')
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Kayıt sayfası"""
    if current_user.is_authenticated:
        return redirect(url_for('topology.dashboard'))
    
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        
        # Kullanıcı adı ve email kontrolü
        if User.query.filter_by(username=username).first():
            flash('Bu kullanıcı adı zaten kullanılıyor.')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Bu email adresi zaten kullanılıyor.')
            return render_template('auth/register.html')
        
        # Yeni kullanıcı oluştur
        user = User(username=username, email=email)
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Kayıt başarılı! Şimdi giriş yapabilirsiniz.')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """Çıkış"""
    logout_user()
    flash('Başarıyla çıkış yaptınız.')
    return redirect(url_for('auth.login'))
