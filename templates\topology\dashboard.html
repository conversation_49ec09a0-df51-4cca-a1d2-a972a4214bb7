{% extends "base/layout.html" %}

{% block title %}Dashboard - <PERSON>ğ Topolojisi Yönetimi{% endblock %}

{% block content %}
<!-- <PERSON><PERSON> Topolojiler -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2>Topolojilerim</h2>
                <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#newTopologyModal">
                    Yeni Topoloji <PERSON>
                </button>
            </div>
            <div class="card-body">
                {% if owned_topologies %}
                <div class="row">
                    {% for topology in owned_topologies %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    {{ topology.name }}
                                    <span class="badge bg-success ms-2"><PERSON><PERSON></span>
                                </h5>
                                <p class="card-text text-muted"><PERSON><PERSON><PERSON><PERSON>ul<PERSON>: {{ topology.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                            </div>
                            <div class="card-footer">
                                <a href="{{ url_for('topology.view_topology', topology_id=topology.id) }}" class="btn btn-primary">Görüntüle</a>
                                <a href="{{ url_for('topology.manage_topology_shares', topology_id=topology.id) }}" class="btn btn-outline-secondary btn-sm">Paylaş</a>
                                <div class="btn-group btn-sm" role="group">
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="renameTopology({{ topology.id }}, '{{ topology.name }}')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteTopology({{ topology.id }}, '{{ topology.name }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    Henüz bir topoloji oluşturmadınız. Yeni bir topoloji eklemek için "Yeni Topoloji Ekle" butonuna tıklayın.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Paylaşılan Topolojiler -->
{% if shared_topologies %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h2>Benimle Paylaşılan Topolojiler</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for topology in shared_topologies %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    {{ topology.name }}
                                    {% if topology.user_permission == 'edit' %}
                                        <span class="badge bg-warning text-dark ms-2">Düzenleme</span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-2">Sadece Okuma</span>
                                    {% endif %}
                                </h5>
                                <p class="card-text text-muted">
                                    Oluşturulma: {{ topology.created_at.strftime('%d.%m.%Y %H:%M') }}<br>
                                    <small>Paylaşan: {{ topology.shared_by_username }}</small>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a href="{{ url_for('topology.view_topology', topology_id=topology.id) }}" class="btn btn-primary">Görüntüle</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Yeni Topoloji Modal -->
<div class="modal fade" id="newTopologyModal" tabindex="-1" aria-labelledby="newTopologyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newTopologyModalLabel">Yeni Topoloji Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('topology.new_topology') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="topologyName" class="form-label">Topoloji Adı</label>
                        <input type="text" class="form-control" id="topologyName" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Ekle</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Topoloji Yeniden Adlandırma Modal -->
<div class="modal fade" id="renameTopologyModal" tabindex="-1" aria-labelledby="renameTopologyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renameTopologyModalLabel">Topoloji Adını Değiştir</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newTopologyName" class="form-label">Yeni Topoloji Adı</label>
                    <input type="text" class="form-control" id="newTopologyName" maxlength="100" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-warning" id="confirmRenameBtn">Değiştir</button>
            </div>
        </div>
    </div>
</div>

<!-- Topoloji Silme Modal -->
<div class="modal fade" id="deleteTopologyModal" tabindex="-1" aria-labelledby="deleteTopologyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteTopologyModalLabel">Topoloji Sil</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Dikkat!</strong> Bu işlem geri alınamaz.
                </div>
                <p>Bu topolojiyi silmek istediğinizden emin misiniz?</p>
                <p><strong id="deleteTopologyInfo"></strong></p>
                <p class="text-muted">
                    <small>
                        • Topolojideki tüm simgeler silinecek<br>
                        • Tüm notlar silinecek<br>
                        • Tüm bağlantılar silinecek<br>
                        • Alt topolojiler silinecek<br>
                        • Paylaşımlar kaldırılacak
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Sil</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentTopologyId = null;

// Topoloji yeniden adlandırma
function renameTopology(topologyId, currentName) {
    currentTopologyId = topologyId;
    document.getElementById('newTopologyName').value = currentName;
    new bootstrap.Modal(document.getElementById('renameTopologyModal')).show();
}

// Topoloji silme
function deleteTopology(topologyId, topologyName) {
    currentTopologyId = topologyId;
    document.getElementById('deleteTopologyInfo').textContent = topologyName;
    new bootstrap.Modal(document.getElementById('deleteTopologyModal')).show();
}

// Yeniden adlandırma onayı
document.getElementById('confirmRenameBtn').addEventListener('click', function() {
    const newName = document.getElementById('newTopologyName').value.trim();
    
    if (!newName) {
        alert('Lütfen geçerli bir isim girin.');
        return;
    }
    
    if (newName.length > 100) {
        alert('İsim 100 karakterden uzun olamaz.');
        return;
    }
    
    fetch(`/api/topology/${currentTopologyId}/rename`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: newName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Sayfayı yenile
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.error);
        }
        
        // Modal'ı kapat
        bootstrap.Modal.getInstance(document.getElementById('renameTopologyModal')).hide();
    })
    .catch(error => {
        console.error('Yeniden adlandırma hatası:', error);
        showAlert('danger', 'Yeniden adlandırma sırasında bir hata oluştu.');
    });
});

// Silme onayı
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    fetch(`/api/topology/${currentTopologyId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Sayfayı yenile
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.error);
        }
        
        // Modal'ı kapat
        bootstrap.Modal.getInstance(document.getElementById('deleteTopologyModal')).hide();
    })
    .catch(error => {
        console.error('Silme hatası:', error);
        showAlert('danger', 'Silme sırasında bir hata oluştu.');
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Sayfanın üstüne ekle
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
