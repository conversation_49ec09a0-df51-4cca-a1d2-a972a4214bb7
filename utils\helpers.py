"""
Yardımcı fonksiyonlar
"""
import os
import random
import string
from models import db, User, Topology, Icon, Note, Connection, TopologyShare, TopologyVersion

def allowed_file(filename, allowed_extensions):
    """Dosya uzantısının izin verilen uzantılardan olup olmadığını kontrol eder"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def delete_topology_recursive(topology):
    """Özyinelemeli olarak topoloji ve içindeki tüm öğeleri siler"""
    try:
        print(f"Siliniyor: {topology.name} (ID: {topology.id})")

        # Alt topolojileri önce sil (parent_id ile)
        sub_topologies = Topology.query.filter_by(parent_id=topology.id).all()
        for sub in sub_topologies:
            delete_topology_recursive(sub)

        # Topolojideki tüm simgelerin ID'lerini al
        icon_ids = [icon.id for icon in Icon.query.filter_by(topology_id=topology.id).all()]

        # Simgelere ait bağlantıları sil (source veya target olarak)
        if icon_ids:
            Connection.query.filter(
                (Connection.source_id.in_(icon_ids)) |
                (Connection.target_id.in_(icon_ids))
            ).delete(synchronize_session=False)

        # Topolojinin bağlantılarını sil
        Connection.query.filter_by(topology_id=topology.id).delete()

        # Simgeleri ve notlarını sil
        for icon_id in icon_ids:
            # Simgenin notlarını sil
            Note.query.filter_by(icon_id=icon_id).delete()

            # Simgenin alt topolojisi varsa, onu da sil
            icon = Icon.query.get(icon_id)
            if icon and icon.sub_topology:
                delete_topology_recursive(icon.sub_topology)

            # Simgeyi sil
            if icon:
                db.session.delete(icon)

        # Topolojinin paylaşımlarını sil
        TopologyShare.query.filter_by(topology_id=topology.id).delete()

        # Topolojinin versiyonlarını sil
        TopologyVersion.query.filter_by(topology_id=topology.id).delete()

        # Topolojiyi sil
        db.session.delete(topology)
        print(f"Silindi: {topology.name}")

    except Exception as e:
        print(f"Silme hatası: {topology.name} - {str(e)}")
        raise e

def generate_random_password(length=8):
    """Rastgele şifre oluşturur"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choices(characters, k=length))

def create_admin_user(username='admin', email='<EMAIL>', password='Admin123!'):
    """Admin kullanıcı oluşturur"""
    # Mevcut admin kullanıcıyı kontrol et
    existing_admin = User.query.filter_by(role='admin').first()
    if existing_admin:
        return False, f"Zaten bir admin kullanıcı mevcut: {existing_admin.username}"

    # Kullanıcı adı ve email çakışması kontrolü
    existing_user = User.query.filter(
        (User.username == username) |
        (User.email == email)
    ).first()

    if existing_user:
        return False, f"Kullanıcı adı veya email zaten kullanılıyor: {existing_user.username}"

    # Yeni admin kullanıcı oluştur
    admin_user = User(
        username=username,
        email=email,
        role='admin',
        is_active_user=True
    )
    admin_user.set_password(password)

    try:
        db.session.add(admin_user)
        db.session.commit()
        return True, "Admin kullanıcı başarıyla oluşturuldu!"
    except Exception as e:
        db.session.rollback()
        return False, f"Admin kullanıcı oluşturulurken hata: {str(e)}"

def get_file_extension(filename):
    """Dosya uzantısını döndürür"""
    if '.' in filename:
        return filename.rsplit('.', 1)[1].lower()
    return ''

def safe_filename(filename):
    """Güvenli dosya adı oluşturur"""
    # Türkçe karakterleri değiştir
    replacements = {
        'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ö': 'o', 'ş': 's', 'ü': 'u',
        'Ç': 'C', 'Ğ': 'G', 'İ': 'I', 'Ö': 'O', 'Ş': 'S', 'Ü': 'U'
    }

    for tr_char, en_char in replacements.items():
        filename = filename.replace(tr_char, en_char)

    # Özel karakterleri kaldır
    safe_chars = string.ascii_letters + string.digits + '.-_'
    filename = ''.join(c for c in filename if c in safe_chars)

    return filename

def format_file_size(size_bytes):
    """Dosya boyutunu okunabilir formata çevirir"""
    if size_bytes == 0:
        return "0B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"
