<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ icon_name }} - Notlar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Summernote CDN -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-bs5.min.css" rel="stylesheet">

    <style>
        body {
            padding: 0;
            margin: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: var(--dark-bg);
            color: white;
            padding: var(--spacing-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .note-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: var(--spacing-md);
        }

        .editor-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            height: calc(100vh - 120px);
        }

        .editor-status-bar {
            display: flex;
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
            align-items: center;
            justify-content: space-between;
        }

        .editor-status {
            font-size: var(--font-size-xs);
            color: var(--text-light);
        }

        .word-count {
            font-size: var(--font-size-xs);
            color: var(--text-light);
        }

        /* Summernote editör özelleştirmeleri */
        .note-editor {
            border: none !important;
            border-radius: 0 !important;
        }

        .note-toolbar {
            background-color: var(--light-bg) !important;
            border-bottom: 1px solid var(--border-color) !important;
            padding: 8px !important;
        }

        .note-editing-area {
            border: none !important;
        }

        .note-editable {
            background-color: white !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            padding: 15px;
        }

        /* Editör yüksekliği */
        #editor {
            height: calc(100vh - 200px);
        }

        /* Summernote toolbar düzeltmeleri */
        .note-toolbar {
            border: 1px solid #ddd !important;
            border-bottom: none !important;
            background: #f8f9fa !important;
            padding: 8px !important;
        }

        .note-btn-group {
            margin-right: 5px !important;
        }

        .note-btn {
            border: 1px solid #ddd !important;
            background: white !important;
            color: #333 !important;
            padding: 6px 12px !important;
            margin: 0 1px !important;
            border-radius: 3px !important;
        }

        .note-btn:hover {
            background: #e9ecef !important;
            border-color: #adb5bd !important;
        }

        .note-btn.active {
            background: #007bff !important;
            color: white !important;
            border-color: #007bff !important;
        }

        /* Dropdown düzeltmeleri */
        .note-dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
            display: none;
            min-width: 160px;
            padding: 5px 0;
            margin: 2px 0 0;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 6px 12px rgba(0,0,0,.175);
        }

        .note-dropdown-menu.show {
            display: block !important;
        }

        .note-dropdown-item {
            display: block;
            width: 100%;
            padding: 3px 20px;
            clear: both;
            font-weight: normal;
            line-height: 1.42857143;
            color: #333;
            white-space: nowrap;
            text-decoration: none;
            background: transparent;
            border: none;
        }

        .note-dropdown-item:hover {
            background-color: #f5f5f5;
            color: #262626;
        }

        /* Font dropdown özel stilleri */
        .note-fontname .dropdown-toggle::after,
        .note-fontsize .dropdown-toggle::after,
        .note-style .dropdown-toggle::after {
            content: "";
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        /* Dropdown toggle butonları */
        .note-btn.dropdown-toggle {
            position: relative;
            padding-right: 20px !important;
        }

        .note-btn.dropdown-toggle::after {
            content: "";
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Editör alanı */
        .note-editable {
            min-height: 400px !important;
            padding: 15px !important;
            border: 1px solid #ddd !important;
            border-top: none !important;
        }

        /* Status bar */
        .editor-status-bar {
            border: 1px solid #ddd !important;
            border-top: none !important;
            background: #f8f9fa !important;
            padding: 8px 15px !important;
            font-size: 12px !important;
            color: #666 !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h4 class="mb-0">{{ icon_name }} - Notlar</h4>
            <small>Topoloji: {{ topology_name }}</small>
        </div>
        <div>
            <button id="back-to-canvas" class="btn btn-sm btn-outline-light">
                <i class="bi bi-arrow-left"></i> Canvas'a Dön
            </button>
        </div>
    </div>

    <div class="note-container">
        <div class="editor-container">
            <!-- Summernote editör buraya yüklenecek -->
            <div id="editor"></div>

            <!-- Durum çubuğu -->
            <div class="editor-status-bar">
                <span class="editor-status" id="editor-status">Yükleniyor...</span>
                <span class="word-count" id="word-count">0 kelime</span>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-bs5.min.js"></script>

    <script>
        const topologyId = {{ topology_id }};
        const iconId = {{ icon_id }};
        const canEdit = {{ can_edit|tojson }};
    </script>
    <script>
        // Data attributes for notes.js
        document.body.dataset.topologyId = {{ topology_id }};
        document.body.dataset.iconId = {{ icon_id }};
        document.body.dataset.iconName = '{{ icon_name }}';
        document.body.dataset.topologyName = '{{ topology_name }}';
        document.body.dataset.readonly = {{ 'true' if not can_edit else 'false' }};
    </script>
    <script src="{{ url_for('static', filename='js/notes.js') }}"></script>
    <script>
        $(document).ready(function() {
            // Canvas'a dön butonu
            document.getElementById('back-to-canvas').addEventListener('click', function() {
                window.location.href = `/topology/${topologyId}`;
            });
        });

        // Dropdown'ları düzelt
        function fixDropdowns() {
            setTimeout(function() {
                console.log('Dropdown düzeltmeleri başlatılıyor...');

                // Summernote DOM yapısını kontrol et
                console.log('Dropdown toggle butonları:', $('[data-toggle="dropdown"]').length);
                console.log('Dropdown menüler:', $('.dropdown-menu').length);

                // Tüm dropdown'ları başlangıçta kapat
                $('.dropdown-menu').hide();

                // Doğru selector ile dropdown'ları yönet
                $(document).off('click.summernoteDropdown').on('click.summernoteDropdown', '[data-toggle="dropdown"]', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Dropdown toggle tıklandı:', $(this).text() || $(this).attr('title') || 'Bilinmeyen');

                    var $button = $(this);
                    var $parent = $button.closest('.note-btn-group');
                    var $dropdown = $parent.find('.dropdown-menu');

                    // Eğer parent'ta bulamazsa, next sibling'i dene
                    if ($dropdown.length === 0) {
                        $dropdown = $button.next('.dropdown-menu');
                    }

                    // Eğer hala bulamazsa, siblings'i dene
                    if ($dropdown.length === 0) {
                        $dropdown = $button.siblings('.dropdown-menu');
                    }

                    console.log('Dropdown menu bulundu:', $dropdown.length);

                    if ($dropdown.length > 0) {
                        // Diğer tüm dropdown'ları kapat
                        $('.dropdown-menu').not($dropdown).hide();

                        // Bu dropdown'ı toggle et
                        if ($dropdown.is(':visible')) {
                            $dropdown.hide();
                            console.log('Dropdown kapatıldı');
                        } else {
                            $dropdown.show();
                            console.log('Dropdown açıldı');
                        }
                    } else {
                        console.log('Dropdown menu bulunamadı!');
                    }
                });

                // Dropdown item'lara tıklandığında dropdown'ı kapat
                $(document).off('click.summernoteDropdownItem').on('click.summernoteDropdownItem', '.dropdown-menu a', function(e) {
                    console.log('Dropdown item seçildi:', $(this).text());
                    setTimeout(function() {
                        $('.dropdown-menu').hide();
                    }, 100);
                });

                // Dışarı tıklandığında dropdown'ları kapat
                $(document).off('click.summernoteOutside').on('click.summernoteOutside', function(e) {
                    if (!$(e.target).closest('.note-btn-group').length &&
                        !$(e.target).closest('.dropdown-menu').length) {
                        $('.dropdown-menu').hide();
                    }
                });

            }, 1500);
        }

        // Summernote yüklenene kadar bekle
        function waitForSummernote() {
            if (typeof $.fn.summernote !== 'undefined') {
                // Summernote yüklendi, editörü başlat
                initializeSummernote();
            } else {
                // Henüz yüklenmedi, 100ms sonra tekrar kontrol et
                setTimeout(waitForSummernote, 100);
            }
        }

        // Summernote editörünü başlat
        function initializeSummernote() {
            const summernoteConfig = {
                height: 'calc(100vh - 200px)',
                fontNames: ['Arial', 'Times New Roman', 'Calibri', 'Georgia', 'Verdana', 'Courier New', 'Tahoma', 'Comic Sans MS', 'Helvetica'],
                fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48', '72'],
                tabsize: 2,
                focus: false,
                dialogsInBody: true,
                disableDragAndDrop: !canEdit,
                callbacks: {
                    onInit: function() {
                        // Editör yüklendikten sonra notları yükle
                        loadNote();
                        editor = $('#editor');

                        // Readonly kullanıcılar için editörü devre dışı bırak
                        if (!canEdit) {
                            $('#editor').summernote('disable');
                            document.getElementById('editor-status').textContent = 'Sadece Okuma Modu';
                        } else {
                            document.getElementById('editor-status').textContent = 'Hazır';
                            // Dropdown'ları düzelt (sadece düzenleme modunda)
                            fixDropdowns();
                        }
                    },
                    onChange: function(contents, $editable) {
                        if (!canEdit) return; // Readonly kullanıcılar için değişiklikleri engelle

                        isEditing = true;
                        document.getElementById('editor-status').textContent = 'Düzenleniyor...';

                        // Kelime sayısını güncelle
                        updateWordCount();

                        // Önceki zamanlayıcıyı temizle
                        if (saveTimeout) {
                            clearTimeout(saveTimeout);
                        }

                        // 2 saniye sonra kaydet
                        saveTimeout = setTimeout(function() {
                            saveNoteIfChanged();
                        }, 2000);
                    },
                    onKeyup: function(e) {
                        updateWordCount();
                    }
                }
            };

            // Düzenleme izni varsa toolbar ekle
            if (canEdit) {
                summernoteConfig.toolbar = [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
                    ['fontname', ['fontname']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'hr']],
                    ['view', ['fullscreen', 'codeview']],
                    ['help', ['help']]
                ];
                summernoteConfig.placeholder = 'Notlarınızı buraya yazın...';
                summernoteConfig.popover = {
                    air: [
                        ['color', ['color']],
                        ['font', ['bold', 'underline', 'clear']]
                    ]
                };
            } else {
                // Readonly mod için toolbar'ı kaldır
                summernoteConfig.toolbar = [];
                summernoteConfig.placeholder = 'Bu not sadece okunabilir...';
                summernoteConfig.popover = {};
            }

            $('#editor').summernote(summernoteConfig);
        }

        // Kelime sayısını güncelle
        function updateWordCount() {
            if (editor && editor.length) {
                const text = $('#editor').summernote('code').replace(/<[^>]*>/g, '').trim();
                const wordCount = text.length > 0 ? text.split(/\s+/).length : 0;
                document.getElementById('word-count').textContent = `${wordCount} kelime`;
            }
        }

        // Not yükleme fonksiyonu
        function loadNote() {
            fetch(`/api/notes/${iconId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.notes && data.notes.length > 0) {
                    // İlk notu al (en son oluşturulan)
                    const note = data.notes[0];
                    $('#editor').summernote('code', note.content || '');
                    lastSavedContent = note.content || '';
                } else {
                    $('#editor').summernote('code', '');
                    lastSavedContent = '';
                }

                document.getElementById('editor-status').textContent = 'Kaydedildi';
                isEditing = false;
                updateWordCount();
            })
            .catch(error => {
                console.error('Not yüklenirken hata oluştu:', error);
                $('#editor').summernote('code', '');
                lastSavedContent = '';
                document.getElementById('editor-status').textContent = 'Yükleme hatası!';
            });
        }

        // Değişiklik varsa notu kaydet
        function saveNoteIfChanged() {
            if (!canEdit) return; // Readonly kullanıcılar kaydedemez

            const noteContent = $('#editor').summernote('code');

            if (noteContent !== lastSavedContent) {
                saveNote(noteContent);
            }
        }

        // Not kaydetme fonksiyonu
        function saveNote(content) {
            if (!canEdit) return; // Readonly kullanıcılar kaydedemez

            document.getElementById('editor-status').textContent = 'Kaydediliyor...';

            fetch(`/api/notes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    icon_id: iconId,
                    content: content
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    lastSavedContent = content;
                    document.getElementById('editor-status').textContent = 'Kaydedildi';
                    isEditing = false;
                } else {
                    document.getElementById('editor-status').textContent = 'Kayıt hatası!';
                    console.error('Not kaydedilirken hata oluştu:', data.error);
                }
            })
            .catch(error => {
                document.getElementById('editor-status').textContent = 'Kayıt hatası!';
                console.error('Not kaydedilirken hata oluştu:', error);
            });
        }
    </script>
</body>
</html>
