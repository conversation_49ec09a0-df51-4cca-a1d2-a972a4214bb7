from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, send_from_directory
from flask_login import Login<PERSON><PERSON>ger, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import uuid
import json
from datetime import datetime

# Uygulama oluşturma
app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///network_topology.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Dosya yükleme ayarları
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif', 'svg'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB

# Uploads klasörünü oluştur (yoksa)
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Veritabanı oluşturma
db = SQLAlchemy(app)

# Login manager oluşturma
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Modeller
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.String(20), default='user')  # 'admin' veya 'user'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active_user = db.Column(db.Boolean, default=True)

    # İlişkiler
    topologies = db.relationship('Topology', backref='owner', lazy=True)
    shared_topologies = db.relationship('TopologyShare', foreign_keys='TopologyShare.user_id', backref='user', lazy=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_authenticated(self):
        return True

    def is_active(self):
        return self.is_active_user

    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    def is_admin(self):
        return self.role == 'admin'

class Topology(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=True)
    parent = db.relationship('Topology', remote_side=[id], backref='children', uselist=False)
    background_image = db.Column(db.String(255), nullable=True)  # Arkaplan resmi URL'si
    background_x = db.Column(db.Float, default=0)  # Arkaplan resmi X konumu
    background_y = db.Column(db.Float, default=0)  # Arkaplan resmi Y konumu
    background_scale = db.Column(db.Float, default=1.0)  # Arkaplan resmi ölçeği
    current_version = db.Column(db.Integer, default=1)  # Mevcut versiyon numarası

    # İlişkiler
    versions = db.relationship('TopologyVersion', backref='topology', lazy='dynamic', cascade='all, delete-orphan')
    shares = db.relationship('TopologyShare', backref='topology', lazy=True, cascade='all, delete-orphan')

    def get_path(self):
        """Topolojinin tam yolunu döndürür (Ana Topoloji > Alt Topoloji > ...)"""
        path = []
        current = self

        while current:
            path.insert(0, current)
            current = current.parent

        return path

    def get_user_permission(self, user_id):
        """Belirli bir kullanıcının bu topoloji için iznini döndürür"""
        if self.user_id == user_id:
            return 'owner'

        share = TopologyShare.query.filter_by(topology_id=self.id, user_id=user_id).first()
        if share:
            return share.permission

        return None

    def can_user_access(self, user_id):
        """Kullanıcının bu topolojiye erişip erişemeyeceğini kontrol eder"""
        return self.get_user_permission(user_id) is not None

    def can_user_edit(self, user_id):
        """Kullanıcının bu topolojiyi düzenleyip düzenleyemeyeceğini kontrol eder"""
        permission = self.get_user_permission(user_id)
        return permission in ['owner', 'edit']

class Icon(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    icon_type = db.Column(db.String(50), nullable=False)  # server, switch, router, firewall, cloud, database, workstation, printer, wireless, storage, loadbalancer
    x_position = db.Column(db.Float, nullable=False)
    y_position = db.Column(db.Float, nullable=False)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    notes = db.relationship('Note', backref='icon', lazy=True)

    # Simgenin alt topolojisi için ilişki
    sub_topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=True)
    sub_topology = db.relationship('Topology', foreign_keys=[sub_topology_id], backref=db.backref('parent_icon', uselist=False))

    def has_sub_topology(self):
        """Simgenin alt topolojisi olup olmadığını kontrol eder"""
        return self.sub_topology_id is not None

class Note(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    icon_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)

class Connection(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=True)
    source_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)
    target_id = db.Column(db.Integer, db.ForeignKey('icon.id'), nullable=False)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # İlişkiler
    source = db.relationship('Icon', foreign_keys=[source_id], backref='outgoing_connections')
    target = db.relationship('Icon', foreign_keys=[target_id], backref='incoming_connections')
    topology = db.relationship('Topology', backref='connections')

class TopologyShare(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission = db.Column(db.String(20), nullable=False)  # 'readonly' veya 'edit'
    shared_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # İlişkiler
    shared_by_user = db.relationship('User', foreign_keys=[shared_by], backref='shared_topologies_by_me')

    # Unique constraint - Aynı kullanıcıya aynı topoloji birden fazla kez paylaşılamaz
    __table_args__ = (db.UniqueConstraint('topology_id', 'user_id', name='unique_topology_user_share'),)

class TopologyVersion(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    version_number = db.Column(db.Integer, nullable=False)
    description = db.Column(db.String(255), nullable=True)
    data = db.Column(db.Text, nullable=False)  # JSON formatında topoloji verisi
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, topology_id, version_number, data, description=None):
        self.topology_id = topology_id
        self.version_number = version_number
        self.data = data
        self.description = description

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Route'lar
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')

        user_exists = User.query.filter_by(username=username).first()
        email_exists = User.query.filter_by(email=email).first()

        if user_exists:
            flash('Kullanıcı adı zaten kullanılıyor.')
            return redirect(url_for('register'))

        if email_exists:
            flash('E-posta adresi zaten kullanılıyor.')
            return redirect(url_for('register'))

        new_user = User(username=username, email=email)
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        flash('Kayıt başarılı! Şimdi giriş yapabilirsiniz.')
        return redirect(url_for('login'))

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if not user or not user.check_password(password):
            flash('Geçersiz kullanıcı adı veya şifre')
            return redirect(url_for('login'))

        login_user(user)
        return redirect(url_for('dashboard'))

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Kullanıcının sahip olduğu topolojiler
    owned_topologies = Topology.query.filter_by(user_id=current_user.id, parent_id=None).all()

    # Kullanıcıyla paylaşılan topolojiler
    shared_topology_ids = db.session.query(TopologyShare.topology_id).filter_by(user_id=current_user.id).subquery()
    shared_topologies = Topology.query.filter(
        Topology.id.in_(shared_topology_ids),
        Topology.parent_id.is_(None)
    ).all()

    # Her paylaşılan topoloji için izin bilgisini ekle
    for topology in shared_topologies:
        share = TopologyShare.query.filter_by(topology_id=topology.id, user_id=current_user.id).first()
        topology.user_permission = share.permission if share else 'readonly'
        topology.shared_by_username = share.shared_by_user.username if share else 'Bilinmeyen'

    return render_template('dashboard.html',
                         owned_topologies=owned_topologies,
                         shared_topologies=shared_topologies)

@app.route('/topology/new', methods=['POST'])
@login_required
def new_topology():
    name = request.form.get('name')
    parent_id = request.form.get('parent_id')
    icon_id = request.form.get('icon_id')  # Simge ID'si (varsa)

    if parent_id:
        parent_id = int(parent_id)
    else:
        parent_id = None

    # Yeni topoloji oluştur
    topology = Topology(name=name, user_id=current_user.id, parent_id=parent_id)
    db.session.add(topology)
    db.session.commit()

    # Eğer bir simge ID'si varsa, simge ile topoloji arasında ilişki kur
    if icon_id:
        icon_id = int(icon_id)
        icon = Icon.query.get(icon_id)
        if icon and icon.topology_id == parent_id:
            icon.sub_topology_id = topology.id
            db.session.commit()

    if parent_id:
        return redirect(url_for('view_topology', topology_id=parent_id))
    return redirect(url_for('dashboard'))

@app.route('/topology/<int:topology_id>')
@login_required
def view_topology(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        flash('Bu topolojiye erişim izniniz yok.')
        return redirect(url_for('dashboard'))

    # Kullanıcının izin seviyesini belirle
    user_permission = topology.get_user_permission(current_user.id)
    can_edit = topology.can_user_edit(current_user.id)

    # Topoloji yolunu al
    topology_path = topology.get_path()

    # Alt topolojileri al
    sub_topologies = Topology.query.filter_by(parent_id=topology_id).all()

    # Üst simgeyi al (varsa)
    parent_icon = topology.parent_icon

    return render_template('canvas.html',
                          topology=topology,
                          sub_topologies=sub_topologies,
                          topology_path=topology_path,
                          parent_icon=parent_icon,
                          user_permission=user_permission,
                          can_edit=can_edit)

# API Route'ları
@app.route('/api/icons', methods=['POST'])
@login_required
def add_icon():
    data = request.json

    icon = Icon(
        name=data['name'],
        icon_type=data['icon_type'],
        x_position=data['x_position'],
        y_position=data['y_position'],
        topology_id=data['topology_id']
    )

    db.session.add(icon)
    db.session.commit()

    return jsonify({'id': icon.id, 'name': icon.name})

@app.route('/api/topology/<int:topology_id>/icons', methods=['GET'])
@login_required
def get_topology_icons(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    icons = Icon.query.filter_by(topology_id=topology_id).all()

    return jsonify([{
        'id': icon.id,
        'name': icon.name,
        'icon_type': icon.icon_type,
        'x_position': icon.x_position,
        'y_position': icon.y_position
    } for icon in icons])

@app.route('/api/icons/<int:icon_id>', methods=['PUT'])
@login_required
def update_icon(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    data = request.json

    icon.x_position = data['x_position']
    icon.y_position = data['y_position']

    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/icons/<int:icon_id>/name', methods=['PUT'])
@login_required
def update_icon_name(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgeyi düzenleme izniniz yok.'}), 403

    data = request.json

    if 'name' not in data or not data['name'].strip():
        return jsonify({'success': False, 'error': 'Geçerli bir isim belirtmelisiniz.'}), 400

    icon.name = data['name'].strip()
    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/icons/<int:icon_id>/notes', methods=['POST'])
@login_required
def add_note(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    data = request.json

    note = Note(
        content=data['content'],
        icon_id=icon_id
    )

    db.session.add(note)
    db.session.commit()

    return jsonify({
        'id': note.id,
        'content': note.content,
        'created_at': note.created_at.isoformat()
    })

@app.route('/api/icons/<int:icon_id>/notes', methods=['GET'])
@login_required
def get_notes(icon_id):
    notes = Note.query.filter_by(icon_id=icon_id).order_by(Note.created_at.desc()).all()

    return jsonify([{
        'id': note.id,
        'content': note.content,
        'created_at': note.created_at.isoformat(),
        'updated_at': note.updated_at.isoformat()
    } for note in notes])

@app.route('/api/icons/<int:icon_id>/note', methods=['GET'])
@login_required
def get_single_note(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Erişim kontrolü
    if not topology.can_user_access(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgenin notlarına erişim izniniz yok.'}), 403

    # Simgenin en son notunu al (yoksa None)
    note = Note.query.filter_by(icon_id=icon_id).order_by(Note.updated_at.desc()).first()

    if note:
        return jsonify({
            'success': True,
            'note': {
                'id': note.id,
                'content': note.content,
                'created_at': note.created_at.isoformat(),
                'updated_at': note.updated_at.isoformat()
            }
        })
    else:
        return jsonify({'success': True, 'note': None})

@app.route('/notes/<int:topology_id>/<int:icon_id>')
@login_required
def view_notes(topology_id, icon_id):
    """Simge notlarını ayrı bir sayfada görüntüle"""
    topology = Topology.query.get_or_404(topology_id)
    icon = Icon.query.get_or_404(icon_id)

    # Erişim kontrolü
    if not topology.can_user_access(current_user.id):
        flash('Bu topolojiye erişim izniniz yok.')
        return redirect(url_for('dashboard'))

    # Simgenin topolojiye ait olduğunu kontrol et
    if icon.topology_id != topology_id:
        flash('Bu simge belirtilen topolojiye ait değil.')
        return redirect(url_for('view_topology', topology_id=topology_id))

    # Kullanıcının düzenleme izni var mı?
    can_edit = topology.can_user_edit(current_user.id)

    # Simge adını al (URL'den veya veritabanından)
    icon_name = request.args.get('name', icon.name)

    return render_template('notes.html',
                          topology_id=topology_id,
                          topology_name=topology.name,
                          icon_id=icon_id,
                          icon_name=icon_name,
                          can_edit=can_edit)

@app.route('/api/icons/<int:icon_id>/note', methods=['PUT'])
@login_required
def update_single_note(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu simgenin notlarını düzenleme izniniz yok.'}), 403

    data = request.json

    if 'content' not in data:
        return jsonify({'success': False, 'error': 'Not içeriği belirtilmemiş.'}), 400

    # Simgenin en son notunu al (yoksa yeni oluştur)
    note = Note.query.filter_by(icon_id=icon_id).order_by(Note.updated_at.desc()).first()

    if note:
        # Mevcut notu güncelle
        note.content = data['content']
        note.updated_at = datetime.utcnow()
    else:
        # Yeni not oluştur
        note = Note(
            content=data['content'],
            icon_id=icon_id
        )
        db.session.add(note)

    db.session.commit()

    return jsonify({'success': True})

# Özyinelemeli olarak topoloji ve içindeki tüm öğeleri silen yardımcı fonksiyon
def delete_topology_recursive(topology):
    # Topolojideki tüm simgeleri bul
    icons = Icon.query.filter_by(topology_id=topology.id).all()

    for icon in icons:
        # Simgenin notlarını sil
        Note.query.filter_by(icon_id=icon.id).delete()

        # Simgenin alt topolojisi varsa, onu da özyinelemeli olarak sil
        if icon.sub_topology:
            delete_topology_recursive(icon.sub_topology)

        # Simgeyi sil
        db.session.delete(icon)

    # Topolojinin bağlantılarını sil
    Connection.query.filter_by(topology_id=topology.id).delete()

    # Topolojinin paylaşımlarını sil
    TopologyShare.query.filter_by(topology_id=topology.id).delete()

    # Topolojinin versiyonlarını sil
    TopologyVersion.query.filter_by(topology_id=topology.id).delete()

    # Alt topolojileri bul ve sil
    sub_topologies = Topology.query.filter_by(parent_id=topology.id).all()
    for sub in sub_topologies:
        delete_topology_recursive(sub)

    # Topolojiyi sil
    db.session.delete(topology)

@app.route('/api/icons/<int:icon_id>', methods=['DELETE'])
@login_required
def delete_icon(icon_id):
    icon = Icon.query.get_or_404(icon_id)
    topology = Topology.query.get_or_404(icon.topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'error': 'Bu simgeyi silme izniniz yok.'}), 403

    # Önce simgeye ait notları sil
    Note.query.filter_by(icon_id=icon_id).delete()

    # Simgenin alt topolojisi varsa, onu ve içindeki tüm öğeleri özyinelemeli olarak sil
    if icon.sub_topology:
        delete_topology_recursive(icon.sub_topology)

    # Simgeyi sil
    db.session.delete(icon)
    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/icons/<int:icon_id>/sub_topology', methods=['GET'])
@login_required
def get_icon_sub_topology(icon_id):
    icon = Icon.query.get_or_404(icon_id)

    if icon.sub_topology:
        return jsonify({
            'has_sub_topology': True,
            'sub_topology_id': icon.sub_topology.id,
            'sub_topology_name': icon.sub_topology.name
        })

    return jsonify({'has_sub_topology': False})

# Bağlantı API endpoint'leri
@app.route('/api/topology/<int:topology_id>/connections', methods=['GET'])
@login_required
def get_connections(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    connections = Connection.query.filter_by(topology_id=topology_id).all()

    return jsonify([{
        'id': conn.id,
        'name': conn.name,
        'source_id': conn.source_id,
        'target_id': conn.target_id,
        'source_name': conn.source.name,
        'target_name': conn.target.name
    } for conn in connections])

@app.route('/api/connections', methods=['POST'])
@login_required
def add_connection():
    data = request.json

    # Gerekli alanları kontrol et
    if not all(key in data for key in ['source_id', 'target_id', 'topology_id']):
        return jsonify({'success': False, 'error': 'Eksik parametreler.'}), 400

    # Topoloji erişim kontrolü - Düzenleme izni gerekli
    topology = Topology.query.get_or_404(data['topology_id'])
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Kaynak ve hedef simgelerin varlığını kontrol et
    source = Icon.query.get_or_404(data['source_id'])
    target = Icon.query.get_or_404(data['target_id'])

    # Simgelerin aynı topolojide olduğunu kontrol et
    if source.topology_id != data['topology_id'] or target.topology_id != data['topology_id']:
        return jsonify({'success': False, 'error': 'Simgeler aynı topolojide olmalıdır.'}), 400

    # Bağlantı oluştur
    connection = Connection(
        name=data.get('name', ''),
        source_id=data['source_id'],
        target_id=data['target_id'],
        topology_id=data['topology_id']
    )

    db.session.add(connection)
    db.session.commit()

    return jsonify({
        'success': True,
        'connection': {
            'id': connection.id,
            'name': connection.name,
            'source_id': connection.source_id,
            'target_id': connection.target_id
        }
    })

@app.route('/api/connections/<int:connection_id>', methods=['PUT'])
@login_required
def update_connection(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    topology = Topology.query.get_or_404(connection.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu bağlantıyı düzenleme izniniz yok.'}), 403

    data = request.json

    # Bağlantı adını güncelle
    if 'name' in data:
        connection.name = data['name']

    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/connections/<int:connection_id>', methods=['DELETE'])
@login_required
def delete_connection(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    topology = Topology.query.get_or_404(connection.topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu bağlantıyı silme izniniz yok.'}), 403

    db.session.delete(connection)
    db.session.commit()

    return jsonify({'success': True})

# Dosya uzantısının izin verilen uzantılardan olup olmadığını kontrol eden fonksiyon
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# Arkaplan resmi yükleme endpoint'i
@app.route('/api/topology/<int:topology_id>/background', methods=['POST'])
@login_required
def upload_background(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Dosya kontrolü
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'Dosya bulunamadı.'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'success': False, 'error': 'Dosya seçilmedi.'}), 400

    if not allowed_file(file.filename):
        return jsonify({'success': False, 'error': 'Geçersiz dosya formatı. İzin verilen formatlar: png, jpg, jpeg, gif, svg'}), 400

    # Uploads klasörünü oluştur (yoksa)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Eski arkaplan resmini sil (varsa)
    if topology.background_image and os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], topology.background_image)):
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], topology.background_image))

    # Benzersiz dosya adı oluştur
    filename = secure_filename(file.filename)
    unique_filename = f"{uuid.uuid4().hex}_{filename}"
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

    # Dosyayı kaydet
    file.save(file_path)

    # Dosyanın kaydedildiğini kontrol et
    if not os.path.exists(file_path):
        return jsonify({'success': False, 'error': 'Dosya kaydedilemedi.'}), 500

    # Veritabanını güncelle
    topology.background_image = unique_filename
    db.session.commit()

    # Tam URL oluştur
    background_url = url_for('static', filename=f'uploads/{unique_filename}')

    print(f"Arkaplan resmi yüklendi: {background_url}")

    return jsonify({
        'success': True,
        'background_image': background_url
    })

# Arkaplan resmini kaldırma endpoint'i
@app.route('/api/topology/<int:topology_id>/background', methods=['DELETE'])
@login_required
def remove_background(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Arkaplan resmi varsa sil
    if topology.background_image:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], topology.background_image)
        if os.path.exists(file_path):
            os.remove(file_path)

        topology.background_image = None
        db.session.commit()

    return jsonify({'success': True})

# Arkaplan resmi bilgisini getirme endpoint'i
@app.route('/api/topology/<int:topology_id>/background', methods=['GET'])
@login_required
def get_background(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    if topology.background_image:
        # Dosyanın var olup olmadığını kontrol et
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], topology.background_image)
        if not os.path.exists(file_path):
            print(f"Arkaplan resmi dosyası bulunamadı: {file_path}")
            topology.background_image = None
            db.session.commit()
            return jsonify({
                'success': True,
                'has_background': False
            })

        background_url = url_for('static', filename=f'uploads/{topology.background_image}')
        print(f"Arkaplan resmi URL'si: {background_url}")

        return jsonify({
            'success': True,
            'has_background': True,
            'background_image': background_url,
            'background_x': topology.background_x,
            'background_y': topology.background_y,
            'background_scale': topology.background_scale
        })
    else:
        return jsonify({
            'success': True,
            'has_background': False
        })

# Arkaplan resmi konumunu güncelleme endpoint'i
@app.route('/api/topology/<int:topology_id>/background/position', methods=['PUT'])
@login_required
def update_background_position(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    # Düzenleme izni kontrolü
    if not topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    if not topology.background_image:
        return jsonify({'success': False, 'error': 'Bu topolojide arkaplan resmi bulunmuyor.'}), 400

    data = request.json

    # X, Y ve ölçek değerlerini güncelle
    if 'x' in data:
        topology.background_x = float(data['x'])
    if 'y' in data:
        topology.background_y = float(data['y'])
    if 'scale' in data:
        topology.background_scale = float(data['scale'])

    db.session.commit()

    return jsonify({
        'success': True,
        'background_x': topology.background_x,
        'background_y': topology.background_y,
        'background_scale': topology.background_scale
    })

# JSON Export endpoint'i
@app.route('/api/topology/<int:topology_id>/export', methods=['GET'])
@login_required
def export_topology(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    # Topoloji temel bilgilerini al
    topology_data = {
        'id': topology.id,
        'name': topology.name,
        'created_at': topology.created_at.isoformat(),
        'background_image': topology.background_image,
        'background_x': topology.background_x,
        'background_y': topology.background_y,
        'background_scale': topology.background_scale,
        'current_version': topology.current_version
    }

    # Simgeleri al
    icons = Icon.query.filter_by(topology_id=topology_id).all()
    icons_data = []

    for icon in icons:
        icon_data = {
            'id': icon.id,
            'name': icon.name,
            'icon_type': icon.icon_type,
            'x_position': icon.x_position,
            'y_position': icon.y_position,
            'sub_topology_id': icon.sub_topology_id
        }

        # Simgenin notunu al
        note = Note.query.filter_by(icon_id=icon.id).order_by(Note.updated_at.desc()).first()
        if note:
            icon_data['note'] = {
                'content': note.content,
                'updated_at': note.updated_at.isoformat()
            }

        icons_data.append(icon_data)

    # Bağlantıları al
    connections = Connection.query.filter_by(topology_id=topology_id).all()
    connections_data = []

    for connection in connections:
        connection_data = {
            'id': connection.id,
            'name': connection.name,
            'source_id': connection.source_id,
            'target_id': connection.target_id
        }
        connections_data.append(connection_data)

    # Tüm verileri birleştir
    export_data = {
        'topology': topology_data,
        'icons': icons_data,
        'connections': connections_data,
        'export_date': datetime.utcnow().isoformat(),
        'export_version': '1.0'
    }

    # JSON dosyası olarak indir
    response = jsonify(export_data)
    response.headers['Content-Disposition'] = f'attachment; filename=topology_{topology_id}_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}.json'
    return response

# JSON Import endpoint'i
@app.route('/api/topology/<int:topology_id>/import', methods=['POST'])
@login_required
def import_topology(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    # Dosya kontrolü
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'Dosya bulunamadı.'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'success': False, 'error': 'Dosya seçilmedi.'}), 400

    if not file.filename.endswith('.json'):
        return jsonify({'success': False, 'error': 'Geçersiz dosya formatı. Sadece JSON dosyaları kabul edilir.'}), 400

    try:
        # JSON dosyasını oku
        import_data = json.loads(file.read().decode('utf-8'))

        # Versiyon oluştur
        try:
            create_topology_version(topology_id, f"İçe aktarma: {file.filename}", json.dumps(import_data))
        except Exception as ve:
            print(f"Versiyon oluşturulurken hata: {str(ve)}")
            # Versiyon oluşturulamazsa devam et

        # Mevcut simgeleri ve bağlantıları temizle
        Icon.query.filter_by(topology_id=topology_id).delete()
        Connection.query.filter_by(topology_id=topology_id).delete()

        # Topoloji bilgilerini güncelle
        topology_data = import_data.get('topology', {})
        topology.name = topology_data.get('name', topology.name)
        topology.background_image = topology_data.get('background_image', topology.background_image)
        topology.background_x = topology_data.get('background_x', topology.background_x)
        topology.background_y = topology_data.get('background_y', topology.background_y)
        topology.background_scale = topology_data.get('background_scale', topology.background_scale)

        # Simgeleri içe aktar
        icon_id_map = {}  # Eski ID -> Yeni ID eşleştirmesi

        for icon_data in import_data.get('icons', []):
            icon = Icon(
                name=icon_data.get('name', ''),
                icon_type=icon_data.get('icon_type', 'server'),
                x_position=icon_data.get('x_position', 0),
                y_position=icon_data.get('y_position', 0),
                topology_id=topology_id
            )
            db.session.add(icon)
            db.session.flush()  # ID'yi almak için flush

            # Eski ID -> Yeni ID eşleştirmesini kaydet
            old_id = icon_data.get('id')
            if old_id:
                icon_id_map[old_id] = icon.id

            # Simgenin notunu içe aktar
            note_data = icon_data.get('note')
            if note_data:
                note = Note(
                    content=note_data.get('content', ''),
                    icon_id=icon.id
                )
                db.session.add(note)

        # Bağlantıları içe aktar
        for conn_data in import_data.get('connections', []):
            # Eski ID'leri yeni ID'lere dönüştür
            old_source_id = conn_data.get('source_id')
            old_target_id = conn_data.get('target_id')

            if old_source_id in icon_id_map and old_target_id in icon_id_map:
                connection = Connection(
                    name=conn_data.get('name', ''),
                    source_id=icon_id_map[old_source_id],
                    target_id=icon_id_map[old_target_id],
                    topology_id=topology_id
                )
                db.session.add(connection)

        db.session.commit()

        return jsonify({'success': True, 'message': 'Topoloji başarıyla içe aktarıldı.'})

    except Exception as e:
        print(f"JSON import sırasında hata: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'İçe aktarma sırasında bir hata oluştu: {str(e)}'}), 500

# Versiyon oluşturma yardımcı fonksiyonu
def create_topology_version(topology_id, description, data=None):
    try:
        topology = Topology.query.get_or_404(topology_id)

        if data is None:
            # Topoloji verilerini al
            export_data = get_topology_export_data(topology_id)
            data = json.dumps(export_data)

        # Yeni versiyon numarası
        if topology.current_version is None:
            topology.current_version = 0

        version_number = topology.current_version + 1

        # Versiyon oluştur
        version = TopologyVersion(
            topology_id=topology_id,
            version_number=version_number,
            description=description,
            data=data
        )

        # Topolojinin mevcut versiyon numarasını güncelle
        topology.current_version = version_number

        db.session.add(version)
        db.session.commit()

        return version
    except Exception as e:
        print(f"Versiyon oluşturulurken hata: {str(e)}")
        db.session.rollback()
        raise e

# Topoloji verilerini alma yardımcı fonksiyonu
def get_topology_export_data(topology_id):
    try:
        topology = Topology.query.get_or_404(topology_id)

        # Topoloji temel bilgilerini al
        topology_data = {
            'id': topology.id,
            'name': topology.name,
            'created_at': topology.created_at.isoformat(),
            'background_image': topology.background_image,
            'background_x': topology.background_x,
            'background_y': topology.background_y,
            'background_scale': topology.background_scale,
            'current_version': topology.current_version
        }

        # Simgeleri al
        icons = Icon.query.filter_by(topology_id=topology_id).all()
        icons_data = []

        for icon in icons:
            icon_data = {
                'id': icon.id,
                'name': icon.name,
                'icon_type': icon.icon_type,
                'x_position': icon.x_position,
                'y_position': icon.y_position,
                'sub_topology_id': icon.sub_topology_id
            }

            # Simgenin notunu al
            note = Note.query.filter_by(icon_id=icon.id).order_by(Note.updated_at.desc()).first()
            if note:
                icon_data['note'] = {
                    'content': note.content,
                    'updated_at': note.updated_at.isoformat()
                }

            icons_data.append(icon_data)

        # Bağlantıları al
        connections = Connection.query.filter_by(topology_id=topology_id).all()
        connections_data = []

        for connection in connections:
            connection_data = {
                'id': connection.id,
                'name': connection.name,
                'source_id': connection.source_id,
                'target_id': connection.target_id
            }
            connections_data.append(connection_data)

        # Tüm verileri birleştir
        export_data = {
            'topology': topology_data,
            'icons': icons_data,
            'connections': connections_data,
            'export_date': datetime.now().isoformat(),
            'export_version': '1.0'
        }

        return export_data
    except Exception as e:
        print(f"Topoloji verileri alınırken hata: {str(e)}")
        raise e

# Versiyon oluşturma endpoint'i
@app.route('/api/topology/<int:topology_id>/versions', methods=['POST'])
@login_required
def create_version(topology_id):
    try:
        topology = Topology.query.get_or_404(topology_id)

        if topology.user_id != current_user.id:
            return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

        data = request.json or {}
        description = data.get('description', f'Manuel versiyon {datetime.now().strftime("%Y-%m-%d %H:%M")}')

        print(f"Versiyon oluşturuluyor: {description}")

        try:
            version = create_topology_version(topology_id, description)

            return jsonify({
                'success': True,
                'version': {
                    'id': version.id,
                    'version_number': version.version_number,
                    'description': version.description,
                    'created_at': version.created_at.isoformat()
                }
            })
        except Exception as e:
            print(f"Versiyon oluşturulurken iç hata: {str(e)}")
            return jsonify({'success': False, 'error': f'Versiyon oluşturulurken bir hata oluştu: {str(e)}'}), 500
    except Exception as e:
        print(f"Versiyon oluşturulurken genel hata: {str(e)}")
        return jsonify({'success': False, 'error': f'Versiyon oluşturulurken bir hata oluştu: {str(e)}'}), 500

# Versiyonları listeleme endpoint'i
@app.route('/api/topology/<int:topology_id>/versions', methods=['GET'])
@login_required
def list_versions(topology_id):
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    try:
        versions = TopologyVersion.query.filter_by(topology_id=topology_id).order_by(TopologyVersion.version_number.desc()).all()

        return jsonify({
            'success': True,
            'current_version': topology.current_version,
            'versions': [{
                'id': version.id,
                'version_number': version.version_number,
                'description': version.description,
                'created_at': version.created_at.isoformat()
            } for version in versions]
        })
    except Exception as e:
        print(f"Versiyonlar listelenirken hata oluştu: {str(e)}")
        return jsonify({'success': False, 'error': f'Versiyonlar listelenirken bir hata oluştu: {str(e)}'}), 500

# Belirli bir versiyonu getirme endpoint'i
@app.route('/api/topology/<int:topology_id>/versions/<int:version_id>', methods=['GET'])
@login_required
def get_version(topology_id, version_id):
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    version = TopologyVersion.query.get_or_404(version_id)

    if version.topology_id != topology_id:
        return jsonify({'success': False, 'error': 'Bu versiyon bu topolojiye ait değil.'}), 400

    return jsonify({
        'success': True,
        'version': {
            'id': version.id,
            'version_number': version.version_number,
            'description': version.description,
            'created_at': version.created_at.isoformat(),
            'data': json.loads(version.data)
        }
    })

# Belirli bir versiyona geri dönme endpoint'i
@app.route('/api/topology/<int:topology_id>/versions/<int:version_id>/restore', methods=['POST'])
@login_required
def restore_version(topology_id, version_id):
    topology = Topology.query.get_or_404(topology_id)

    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiye erişim izniniz yok.'}), 403

    version = TopologyVersion.query.get_or_404(version_id)

    if version.topology_id != topology_id:
        return jsonify({'success': False, 'error': 'Bu versiyon bu topolojiye ait değil.'}), 400

    try:
        # Önce mevcut durumun versiyonunu oluştur
        create_topology_version(topology_id, f"Versiyon {version.version_number}'e geri dönmeden önceki durum")

        # Versiyondaki verileri yükle
        import_data = json.loads(version.data)

        # Mevcut simgeleri ve bağlantıları temizle
        Icon.query.filter_by(topology_id=topology_id).delete()
        Connection.query.filter_by(topology_id=topology_id).delete()

        # Topoloji bilgilerini güncelle
        topology_data = import_data.get('topology', {})
        topology.name = topology_data.get('name', topology.name)
        topology.background_image = topology_data.get('background_image', topology.background_image)
        topology.background_x = topology_data.get('background_x', topology.background_x)
        topology.background_y = topology_data.get('background_y', topology.background_y)
        topology.background_scale = topology_data.get('background_scale', topology.background_scale)

        # Simgeleri içe aktar
        icon_id_map = {}  # Eski ID -> Yeni ID eşleştirmesi

        for icon_data in import_data.get('icons', []):
            icon = Icon(
                name=icon_data.get('name', ''),
                icon_type=icon_data.get('icon_type', 'server'),
                x_position=icon_data.get('x_position', 0),
                y_position=icon_data.get('y_position', 0),
                topology_id=topology_id
            )
            db.session.add(icon)
            db.session.flush()  # ID'yi almak için flush

            # Eski ID -> Yeni ID eşleştirmesini kaydet
            old_id = icon_data.get('id')
            if old_id:
                icon_id_map[old_id] = icon.id

            # Simgenin notunu içe aktar
            note_data = icon_data.get('note')
            if note_data:
                note = Note(
                    content=note_data.get('content', ''),
                    icon_id=icon.id
                )
                db.session.add(note)

        # Bağlantıları içe aktar
        for conn_data in import_data.get('connections', []):
            # Eski ID'leri yeni ID'lere dönüştür
            old_source_id = conn_data.get('source_id')
            old_target_id = conn_data.get('target_id')

            if old_source_id in icon_id_map and old_target_id in icon_id_map:
                connection = Connection(
                    name=conn_data.get('name', ''),
                    source_id=icon_id_map[old_source_id],
                    target_id=icon_id_map[old_target_id],
                    topology_id=topology_id
                )
                db.session.add(connection)

        db.session.commit()

        return jsonify({'success': True, 'message': f'Topoloji başarıyla versiyon {version.version_number}\'e geri döndürüldü.'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'Versiyon geri yüklenirken bir hata oluştu: {str(e)}'}), 500

# Kullanıcı Yönetimi API Route'ları

@app.route('/api/users/search', methods=['GET'])
@login_required
def search_users():
    """Kullanıcı arama - email veya username ile"""
    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify({'success': False, 'error': 'En az 2 karakter girmelisiniz.'})

    # Mevcut kullanıcıyı hariç tut
    users = User.query.filter(
        User.id != current_user.id,
        User.is_active_user == True,
        db.or_(
            User.username.ilike(f'%{query}%'),
            User.email.ilike(f'%{query}%')
        )
    ).limit(10).all()

    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email
        })

    return jsonify({'success': True, 'users': users_data})

@app.route('/api/topology/<int:topology_id>/share', methods=['POST'])
@login_required
def share_topology(topology_id):
    """Topolojiyi kullanıcıyla paylaş"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip paylaşabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi paylaşma yetkiniz yok.'}), 403

    data = request.json
    user_id = data.get('user_id')
    permission = data.get('permission', 'readonly')

    if permission not in ['readonly', 'edit']:
        return jsonify({'success': False, 'error': 'Geçersiz izin türü.'}), 400

    # Kullanıcının varlığını kontrol et
    target_user = User.query.get(user_id)
    if not target_user:
        return jsonify({'success': False, 'error': 'Kullanıcı bulunamadı.'}), 404

    # Kendisiyle paylaşmaya çalışıyor mu?
    if user_id == current_user.id:
        return jsonify({'success': False, 'error': 'Topolojiyi kendinizle paylaşamazsınız.'}), 400

    try:
        # Mevcut paylaşımı kontrol et
        existing_share = TopologyShare.query.filter_by(
            topology_id=topology_id,
            user_id=user_id
        ).first()

        if existing_share:
            # Mevcut paylaşımı güncelle
            existing_share.permission = permission
            existing_share.created_at = datetime.utcnow()
        else:
            # Yeni paylaşım oluştur
            share = TopologyShare(
                topology_id=topology_id,
                user_id=user_id,
                permission=permission,
                shared_by=current_user.id
            )
            db.session.add(share)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Topoloji {target_user.username} ile {permission} izniyle paylaşıldı.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Paylaşım sırasında bir hata oluştu.'}), 500

@app.route('/api/topology/<int:topology_id>/shares', methods=['GET'])
@login_required
def get_topology_shares(topology_id):
    """Topolojinin paylaşım listesini getir"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip görebilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojinin paylaşımlarını görme yetkiniz yok.'}), 403

    shares = TopologyShare.query.filter_by(topology_id=topology_id).all()

    shares_data = []
    for share in shares:
        shares_data.append({
            'id': share.id,
            'user_id': share.user_id,
            'username': share.user.username,
            'email': share.user.email,
            'permission': share.permission,
            'created_at': share.created_at.isoformat()
        })

    return jsonify({'success': True, 'shares': shares_data})

@app.route('/api/topology/<int:topology_id>/share/<int:share_id>', methods=['DELETE'])
@login_required
def remove_topology_share(topology_id, share_id):
    """Topoloji paylaşımını kaldır"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip kaldırabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu paylaşımı kaldırma yetkiniz yok.'}), 403

    share = TopologyShare.query.get_or_404(share_id)

    # Paylaşımın bu topolojiye ait olduğunu kontrol et
    if share.topology_id != topology_id:
        return jsonify({'success': False, 'error': 'Geçersiz paylaşım.'}), 400

    try:
        db.session.delete(share)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Paylaşım kaldırıldı.'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Paylaşım kaldırılırken bir hata oluştu.'}), 500

@app.route('/topology/<int:topology_id>/manage')
@login_required
def manage_topology_shares(topology_id):
    """Topoloji paylaşım yönetimi sayfası"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip yönetebilir
    if topology.user_id != current_user.id:
        flash('Bu topolojinin paylaşımlarını yönetme yetkiniz yok.')
        return redirect(url_for('dashboard'))

    return render_template('manage_shares.html', topology=topology)

# Topoloji Yönetimi API Route'ları

@app.route('/api/topology/<int:topology_id>/rename', methods=['PUT'])
@login_required
def rename_topology(topology_id):
    """Topoloji adını değiştir"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip yeniden adlandırabilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi yeniden adlandırma yetkiniz yok.'}), 403

    data = request.json
    new_name = data.get('name', '').strip()

    if not new_name:
        return jsonify({'success': False, 'error': 'Geçerli bir isim belirtmelisiniz.'}), 400

    if len(new_name) > 100:
        return jsonify({'success': False, 'error': 'İsim 100 karakterden uzun olamaz.'}), 400

    try:
        topology.name = new_name
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Topoloji adı başarıyla değiştirildi.',
            'new_name': new_name
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Ad değiştirme sırasında bir hata oluştu.'}), 500

@app.route('/api/topology/<int:topology_id>', methods=['DELETE'])
@login_required
def delete_topology_api(topology_id):
    """Topolojiyi sil"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip silebilir
    if topology.user_id != current_user.id:
        return jsonify({'success': False, 'error': 'Bu topolojiyi silme yetkiniz yok.'}), 403

    try:
        # Özyinelemeli olarak topoloji ve tüm alt öğelerini sil
        delete_topology_recursive(topology)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Topoloji başarıyla silindi.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Topoloji silinirken bir hata oluştu.'}), 500

# Admin Kullanıcı Yönetimi Route'ları

@app.route('/admin')
@login_required
def admin_dashboard():
    """Admin dashboard"""
    if not current_user.is_admin():
        flash('Bu sayfaya erişim yetkiniz yok.')
        return redirect(url_for('dashboard'))

    return render_template('admin/dashboard.html')

@app.route('/admin/users')
@login_required
def admin_users():
    """Kullanıcı yönetimi sayfası"""
    if not current_user.is_admin():
        flash('Bu sayfaya erişim yetkiniz yok.')
        return redirect(url_for('dashboard'))

    return render_template('admin/users.html')

@app.route('/api/admin/users', methods=['GET'])
@login_required
def get_all_users():
    """Tüm kullanıcıları listele"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403

    users = User.query.all()
    users_data = []

    for user in users:
        # Kullanıcının topoloji sayısını hesapla
        topology_count = Topology.query.filter_by(user_id=user.id, parent_id=None).count()

        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active_user,
            'created_at': user.created_at.isoformat(),
            'topology_count': topology_count
        })

    return jsonify({'success': True, 'users': users_data})

@app.route('/api/admin/users/<int:user_id>/toggle-status', methods=['PUT'])
@login_required
def toggle_user_status(user_id):
    """Kullanıcı durumunu aktif/pasif yap"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403

    user = User.query.get_or_404(user_id)

    # Kendini devre dışı bırakmasını engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi hesabınızı devre dışı bırakamazsınız.'}), 400

    try:
        user.is_active_user = not user.is_active_user
        db.session.commit()

        status = 'aktif' if user.is_active_user else 'pasif'
        return jsonify({
            'success': True,
            'message': f'Kullanıcı {status} duruma getirildi.',
            'is_active': user.is_active_user
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Durum değiştirme sırasında bir hata oluştu.'}), 500

@app.route('/api/admin/users/<int:user_id>/reset-password', methods=['PUT'])
@login_required
def reset_user_password(user_id):
    """Kullanıcı şifresini sıfırla"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403

    user = User.query.get_or_404(user_id)

    try:
        # Yeni şifre oluştur (8 karakter, alfanumerik)
        import random
        import string
        new_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))

        user.set_password(new_password)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Şifre başarıyla sıfırlandı.',
            'new_password': new_password
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Şifre sıfırlama sırasında bir hata oluştu.'}), 500

@app.route('/api/admin/users/<int:user_id>/change-role', methods=['PUT'])
@login_required
def change_user_role(user_id):
    """Kullanıcı rolünü değiştir"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403

    user = User.query.get_or_404(user_id)
    data = request.json
    new_role = data.get('role')

    if new_role not in ['admin', 'user']:
        return jsonify({'success': False, 'error': 'Geçersiz rol.'}), 400

    # Kendinin rolünü değiştirmesini engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi rolünüzü değiştiremezsiniz.'}), 400

    try:
        user.role = new_role
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Kullanıcı rolü {new_role} olarak değiştirildi.',
            'new_role': new_role
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Rol değiştirme sırasında bir hata oluştu.'}), 500

@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    """Kullanıcıyı sil"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403

    user = User.query.get_or_404(user_id)

    # Kendini silmesini engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi hesabınızı silemezsiniz.'}), 400

    try:
        # Kullanıcının topolojilerini sil
        topologies = Topology.query.filter_by(user_id=user_id).all()
        for topology in topologies:
            delete_topology_recursive(topology)

        # Kullanıcının paylaşımlarını sil
        TopologyShare.query.filter_by(user_id=user_id).delete()
        TopologyShare.query.filter_by(shared_by=user_id).delete()

        # Kullanıcıyı sil
        db.session.delete(user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Kullanıcı başarıyla silindi.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Kullanıcı silinirken bir hata oluştu.'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(host='0.0.0.0', port=3000)
