{% extends "base/layout.html" %}

{% block title %}Admin Dashboard - Ağ Topolojisi Yönetimi{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">
                <i class="bi bi-speedometer2"></i> Admin Dashboard
            </h1>
        </div>
    </div>

    <!-- İstatistik Kartları -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalUsers">-</h4>
                            <p class="card-text">Toplam Kullanıcı</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="activeUsers">-</h4>
                            <p class="card-text">Aktif Kullanıcı</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalTopologies">-</h4>
                            <p class="card-text">Toplam Topoloji</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-diagram-3 fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="adminUsers">-</h4>
                            <p class="card-text">Admin Kullanıcı</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-shield-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hızlı Erişim -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> Hızlı Erişim
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="bi bi-people"></i> Kullanıcı Yönetimi
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-success btn-lg" onclick="refreshStats()">
                                    <i class="bi bi-arrow-clockwise"></i> İstatistikleri Yenile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/admin.js') }}"></script>
<script>
// Sayfa yüklendiğinde istatistikleri getir
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
});

function loadStats() {
    fetch('/admin/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const users = data.users;

                // Toplam kullanıcı
                document.getElementById('totalUsers').textContent = users.length;

                // Aktif kullanıcı
                const activeUsers = users.filter(user => user.is_active).length;
                document.getElementById('activeUsers').textContent = activeUsers;

                // Admin kullanıcı
                const adminUsers = users.filter(user => user.role === 'admin').length;
                document.getElementById('adminUsers').textContent = adminUsers;

                // Toplam topoloji
                const totalTopologies = users.reduce((sum, user) => sum + user.topology_count, 0);
                document.getElementById('totalTopologies').textContent = totalTopologies;
            } else {
                console.error('İstatistikler yüklenemedi:', data.error);
            }
        })
        .catch(error => {
            console.error('İstatistikler yüklenirken hata:', error);
        });
}

function refreshStats() {
    // Yükleme göstergesi
    document.getElementById('totalUsers').textContent = '...';
    document.getElementById('activeUsers').textContent = '...';
    document.getElementById('totalTopologies').textContent = '...';
    document.getElementById('adminUsers').textContent = '...';

    // İstatistikleri yenile
    loadStats();
}
</script>
{% endblock %}
