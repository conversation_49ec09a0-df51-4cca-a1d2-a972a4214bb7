{% extends "base/layout.html" %}

{% block title %}{{ topology.name }} - <PERSON>ğ Topolojisi Yönetimi{% endblock %}

{% block extra_css %}
<style>
    #canvas-container {
        position: relative;
        width: 100%;
        height: calc(100vh - 150px);
        border: 1px solid #ccc;
        overflow: hidden;
    }

    #topology-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f8f9fa;
    }

    .canvas-layers {
        position: relative;
        width: 100%;
        height: 100%;
        transform-origin: 0 0;
    }

    .icon {
        position: absolute;
        width: 80px;
        height: 80px;
        cursor: pointer;
        text-align: center;
        user-select: none;
    }

    .icon-image {
        font-size: 40px;
        margin-bottom: 5px;
        color: #333;
    }

    /* n8n tarzı arka plan renkleri */
    .icon-server { background-color: #3b82f6; }
    .icon-switch { background-color: #10b981; }
    .icon-router { background-color: #f59e0b; }
    .icon-firewall { background-color: #ef4444; }
    .icon-cloud { background-color: #06b6d4; }
    .icon-database { background-color: #8b5cf6; }
    .icon-workstation { background-color: #6366f1; }
    .icon-printer { background-color: #14b8a6; }
    .icon-wireless { background-color: #ec4899; }
    .icon-storage { background-color: #6b7280; }
    .icon-loadbalancer { background-color: #374151; }

    .icon-palette {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        box-shadow: 0 -4px 15px rgba(0,0,0,0.15);
        padding: 10px;
        z-index: 1000;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        width: 100%;
    }

    .icon-palette-item {
        width: 80px;
        height: 80px;
        margin: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        position: relative;
    }

    .icon-palette-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .icon-palette-item i {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        border-radius: 8px;
        margin-bottom: 5px;
    }

    .icon-palette-item span {
        font-size: 11px;
        text-align: center;
        color: #374151;
        font-weight: 500;
    }

    .icon-name {

        font-size: 12px;
    }

    .icon.selected {
        outline: 2px solid #007bff;
        background-color: rgba(0, 123, 255, 0.1);
        border-radius: 5px;
    }

    /* Bağlantı noktaları */
    .connection-point {
        position: absolute;
        width: 16px;
        height: 16px;
        background-color: #007bff;
        border: 3px solid #fff;
        border-radius: 50%;
        cursor: crosshair;
        opacity: 0;
        transition: all 0.2s ease;
        z-index: 100;
        box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    }

    .connection-point:hover {
        opacity: 1 !important;
        transform: scale(1.2);
        background-color: #0056b3 !important;
    }

    /* n8n tarzı bağlantı çizgileri */
    .connection-line {
        stroke: #007bff;
        stroke-width: 2;
        fill: none;
        pointer-events: stroke;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .connection-line:hover {
        stroke: #0056b3;
        stroke-width: 3;
    }

    .temp-connection-line {
        stroke: #007bff;
        stroke-width: 2;
        fill: none;
        pointer-events: none;
        stroke-dasharray: 5,5;
        animation: dash 1s linear infinite;
    }

    @keyframes dash {
        to {
            stroke-dashoffset: -10;
        }
    }

    /* Geçici bağlantı çizgisi animasyonu */
    @keyframes dash-line {
        0% {
            background-position: 0 0;
        }
        100% {
            background-position: 20px 0;
        }
    }

    /* Canvas bildirim */
    .canvas-notification {
        animation: slideInRight 0.3s ease;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .breadcrumb-item a {
        text-decoration: none;
        color: #6c757d;
    }

    .breadcrumb-item a:hover {
        color: #007bff;
    }

    .breadcrumb-item.active {
        color: #495057;
        font-weight: 500;
    }

    /* Zoom ve Pan Kontrolleri */
    .zoom-controls {
        position: fixed;
        bottom: 24px;
        left: 215px; /* Minimap'in yanında */
        z-index: 1001;
        display: flex;
        flex-direction: column;
        gap: 2px;
        background: white;
        padding: 4px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
        border: 1px solid #e1e5e9;
        width: fit-content;
        height: fit-content;
        transform: scale(0.6);
        transform-origin: bottom left; /* Sol tarafa hizalı */
    }

    .zoom-btn {
        width: 40px;
        height: 40px;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #333;
        transition: all 0.2s ease;
        user-select: none;
    }

    .zoom-btn:hover {
        background: #f8f9fa;
        border-color: #007bff;
        color: #007bff;
        transform: scale(1.05);
    }

    .zoom-level {
        background: #f8f9fa;
        padding: 8px;
        border-radius: 6px;
        font-size: 12px;
        text-align: center;
        color: #333;
        font-weight: 500;
        border: 1px solid #e1e5e9;
        min-width: 40px;
    }

    /* Mini Map */
    .mini-map {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 200px;
        height: 150px;
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        overflow: hidden;
        display: block !important;
    }

    .mini-map-canvas {
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .mini-map-viewport {
        position: absolute;
        border: 2px solid #007bff;
        background: rgba(0, 123, 255, 0.1);
        pointer-events: none;
    }

    /* Canvas Container */
    #canvas-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        height: calc(100vh - 200px);
        cursor: grab;
    }

    #canvas-container.panning {
        cursor: grabbing;
    }

    .canvas-layers {
        position: relative;
        transform-origin: 0 0;
        transition: transform 0.2s ease;
    }

    /* Context Menu Styles */
    .context-menu {
        position: fixed;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 8px 0;
        min-width: 180px;
        z-index: 10000;
        display: none;
        font-size: 14px;
    }

    .context-menu-item {
        padding: 10px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: background-color 0.2s;
    }

    .context-menu-item:hover {
        background-color: #f5f5f5;
    }

    .context-menu-item.danger:hover {
        background-color: #fee;
        color: #dc3545;
    }

    .context-menu-separator {
        height: 1px;
        background-color: #eee;
        margin: 4px 0;
    }

    .context-menu-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<div class="container-fluid py-2 bg-light border-bottom">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ url_for('topology.dashboard') }}"><i class="bi bi-house"></i> Dashboard</a></li>
            {% for path_item in topology_path[:-1] %}
            <li class="breadcrumb-item">
                <a href="{{ url_for('topology.view_topology', topology_id=path_item.id) }}">
                    <i class="bi bi-diagram-3"></i> {{ path_item.display_name if path_item.display_name else path_item.name }}
                </a>
            </li>
            {% endfor %}
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-diagram-3"></i> {{ topology.display_name if topology.display_name else topology.name }}
            </li>
        </ol>
    </nav>
</div>

<div class="row g-0">
    <div class="col-12">
        <div class="card rounded-0 h-100">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-2">
                <h5 class="mb-0 text-dark">{{ topology.name }}</h5>
                <div>
                    <button id="toggle-palette" class="btn btn-outline-secondary btn-sm me-2">
                        <i class="bi bi-grid-3x3-gap-fill"></i> Simge Paleti
                    </button>
                    <button id="toggle-connection-mode" class="btn btn-outline-primary btn-sm me-2">
                        <i class="bi bi-link-45deg"></i> Bağlantı Oluştur
                    </button>
                    <button id="delete-connection" class="btn btn-outline-danger btn-sm me-2" style="display: none;">
                        <i class="bi bi-trash"></i> Bağlantıyı Sil
                    </button>
                    <button id="delete-icon" class="btn btn-outline-danger btn-sm me-2" style="display: none;">
                        <i class="bi bi-trash"></i> Simgeyi Sil
                    </button>

                    <!-- Export/Import/Versioning Buttons -->
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-success btn-sm" onclick="exportTopology()">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="showImportDialog()">
                            <i class="bi bi-upload"></i> Import
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showVersionDialog()">
                            <i class="bi bi-clock-history"></i> Versiyonlar
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Konva.js Canvas Container -->
                <div id="konva-container" style="width: 100%; height: calc(100vh - 150px); position: relative; overflow: hidden;">
                </div>

                <div id="notes-container" style="display: none;">
                    <div id="note-content" style="display: none;"></div>
                    <span id="selected-icon-name" style="display: none;"></span>
                    <span id="editor-status" style="display: none;"></span>
                </div>

                <!-- Zoom Kontrolleri -->
                <div class="zoom-controls">
                    <button class="zoom-btn" id="zoom-in" title="Yakınlaştır">
                        <i class="bi bi-plus"></i>
                    </button>
                    <div class="zoom-level" id="zoom-level">100%</div>
                    <button class="zoom-btn" id="zoom-out" title="Uzaklaştır">
                        <i class="bi bi-dash"></i>
                    </button>
                    <button class="zoom-btn" id="zoom-fit" title="Tümünü Göster">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                    <button class="zoom-btn" id="zoom-reset" title="Sıfırla">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>

                <!-- Mini Map -->
                <div class="mini-map" id="mini-map">
                    <canvas class="mini-map-canvas" id="mini-map-canvas"></canvas>
                    <div class="mini-map-viewport" id="mini-map-viewport"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Simge Paleti -->
<div class="icon-palette" id="icon-palette" style="display: none;">
    <div class="icon-palette-item" data-type="server">
        <i class="bi bi-hdd-rack icon-server"></i>
        <span>Server</span>
    </div>
    <div class="icon-palette-item" data-type="switch">
        <i class="bi bi-ethernet icon-switch"></i>
        <span>Switch</span>
    </div>
    <div class="icon-palette-item" data-type="router">
        <i class="bi bi-router icon-router"></i>
        <span>Router</span>
    </div>
    <div class="icon-palette-item" data-type="firewall">
        <i class="bi bi-shield-lock icon-firewall"></i>
        <span>Firewall</span>
    </div>
    <div class="icon-palette-item" data-type="cloud">
        <i class="bi bi-cloud icon-cloud"></i>
        <span>Cloud</span>
    </div>
    <div class="icon-palette-item" data-type="database">
        <i class="bi bi-database icon-database"></i>
        <span>Database</span>
    </div>
    <div class="icon-palette-item" data-type="workstation">
        <i class="bi bi-pc-display icon-workstation"></i>
        <span>Workstation</span>
    </div>
    <div class="icon-palette-item" data-type="printer">
        <i class="bi bi-printer icon-printer"></i>
        <span>Printer</span>
    </div>
    <div class="icon-palette-item" data-type="wireless">
        <i class="bi bi-wifi icon-wireless"></i>
        <span>Wireless</span>
    </div>
    <div class="icon-palette-item" data-type="storage">
        <i class="bi bi-device-hdd icon-storage"></i>
        <span>Storage</span>
    </div>
    <div class="icon-palette-item" data-type="loadbalancer">
        <i class="bi bi-diagram-3 icon-loadbalancer"></i>
        <span>Load Balancer</span>
    </div>
</div>

<!-- Context Menu -->
<div class="context-menu" id="context-menu">
    <!-- Tek seçim menüsü -->
    <div class="single-selection-menu">
        <div class="context-menu-item" id="context-open-notes">
            <div class="context-menu-icon">
                <i class="bi bi-journal-text"></i>
            </div>
            <span>Notları Aç</span>
        </div>
        <div class="context-menu-item" id="context-edit-name">
            <div class="context-menu-icon">
                <i class="bi bi-pencil"></i>
            </div>
            <span>Adını Düzenle</span>
        </div>
        <div class="context-menu-separator"></div>
    </div>

    <!-- Çoklu seçim menüsü -->
    <div class="multi-selection-menu" style="display: none;">
        <div class="context-menu-item" id="context-align-left">
            <div class="context-menu-icon">
                <i class="bi bi-align-start"></i>
            </div>
            <span>Sola Hizala</span>
        </div>
        <div class="context-menu-item" id="context-align-center">
            <div class="context-menu-icon">
                <i class="bi bi-align-center"></i>
            </div>
            <span>Ortaya Hizala</span>
        </div>
        <div class="context-menu-item" id="context-align-right">
            <div class="context-menu-icon">
                <i class="bi bi-align-end"></i>
            </div>
            <span>Sağa Hizala</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" id="context-align-top">
            <div class="context-menu-icon">
                <i class="bi bi-align-top"></i>
            </div>
            <span>Üste Hizala</span>
        </div>
        <div class="context-menu-item" id="context-align-middle">
            <div class="context-menu-icon">
                <i class="bi bi-align-middle"></i>
            </div>
            <span>Dikey Ortala</span>
        </div>
        <div class="context-menu-item" id="context-align-bottom">
            <div class="context-menu-icon">
                <i class="bi bi-align-bottom"></i>
            </div>
            <span>Alta Hizala</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" id="context-distribute-horizontal">
            <div class="context-menu-icon">
                <i class="bi bi-distribute-horizontal"></i>
            </div>
            <span>Yatay Boşlukları Eşitle</span>
        </div>
        <div class="context-menu-item" id="context-distribute-vertical">
            <div class="context-menu-icon">
                <i class="bi bi-distribute-vertical"></i>
            </div>
            <span>Dikey Boşlukları Eşitle</span>
        </div>
        <div class="context-menu-separator"></div>
    </div>

    <!-- Ortak menü öğeleri -->
    <div class="context-menu-item danger" id="context-delete">
        <div class="context-menu-icon">
            <i class="bi bi-trash"></i>
        </div>
        <span>Sil</span>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Topoloji İçe Aktar</h5>
                <button type="button" class="btn-close" onclick="hideImportModal()"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">JSON Dosyası Seçin</label>
                        <input type="file" class="form-control" id="importFile" accept=".json" required>
                        <div class="form-text">Sadece .json uzantılı dosyalar kabul edilir.</div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Uyarı:</strong> İçe aktarma işlemi mevcut topolojiyi tamamen değiştirecektir.
                        İşlem öncesi otomatik bir versiyon oluşturulacaktır.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideImportModal()">İptal</button>
                <button type="button" class="btn btn-warning" onclick="importTopology()">İçe Aktar</button>
            </div>
        </div>
    </div>
</div>

<!-- Version Modal -->
<div class="modal fade" id="versionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Topoloji Versiyonları</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>Mevcut Versiyonlar</h6>
                    <button class="btn btn-primary btn-sm" onclick="createNewVersion()">
                        <i class="bi bi-plus"></i> Yeni Versiyon Oluştur
                    </button>
                </div>
                <div id="versionList">
                    <!-- Versiyonlar buraya yüklenecek -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Version Modal -->
<div class="modal fade" id="createVersionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Yeni Versiyon Oluştur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createVersionForm">
                    <div class="mb-3">
                        <label for="versionDescription" class="form-label">Versiyon Açıklaması</label>
                        <input type="text" class="form-control" id="versionDescription"
                               placeholder="Örn: Yeni sunucu eklendi" required>
                        <div class="form-text">Bu versiyon için kısa bir açıklama girin.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" onclick="saveNewVersion()">Versiyon Oluştur</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Konva.js Kütüphanesi -->
<script src="https://unpkg.com/konva@9/konva.min.js"></script>
<script>
    // Global değişkenler canvas.js için
    window.topologyId = {{ topology.id }};
    window.canEdit = {{ 'true' if can_edit else 'false' }};
</script>
<script src="{{ url_for('static', filename='js/canvas.js') }}"></script>
{% endblock %}
