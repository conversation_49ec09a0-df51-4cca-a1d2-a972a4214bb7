"""
Topoloji route'ları
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Topology, TopologyShare, Icon

topology_bp = Blueprint('topology', __name__)

@topology_bp.route('/dashboard')
@login_required
def dashboard():
    """Dashboard sayfası"""
    # Kullanıcının sahip olduğu topolojiler
    owned_topologies = Topology.query.filter_by(user_id=current_user.id, parent_id=None).all()

    # Kullanıcıyla paylaşılan topolojiler
    shared_topology_ids = db.session.query(TopologyShare.topology_id).filter_by(user_id=current_user.id).subquery()
    shared_topologies = Topology.query.filter(
        Topology.id.in_(shared_topology_ids),
        Topology.parent_id.is_(None)
    ).all()

    # Her paylaşılan topoloji için izin bilgisini ekle
    for topology in shared_topologies:
        share = TopologyShare.query.filter_by(topology_id=topology.id, user_id=current_user.id).first()
        topology.user_permission = share.permission if share else 'readonly'
        topology.shared_by_username = share.shared_by_user.username if share else 'Bilinmeyen'

    return render_template('topology/dashboard.html',
                         owned_topologies=owned_topologies,
                         shared_topologies=shared_topologies)

@topology_bp.route('/topology/new', methods=['POST'])
@login_required
def new_topology():
    """Yeni topoloji oluştur"""
    name = request.form.get('name')
    parent_id = request.form.get('parent_id')
    icon_id = request.form.get('icon_id')  # Simge ID'si (varsa)

    if parent_id:
        parent_id = int(parent_id)
    else:
        parent_id = None

    # Yeni topoloji oluştur
    topology = Topology(name=name, user_id=current_user.id, parent_id=parent_id)
    db.session.add(topology)
    db.session.commit()

    # Eğer bir simge ID'si varsa, simge ile topoloji arasında ilişki kur
    if icon_id:
        icon_id = int(icon_id)
        icon = Icon.query.get(icon_id)
        if icon and icon.topology_id == parent_id:
            icon.sub_topology_id = topology.id
            db.session.commit()

    if parent_id:
        return redirect(url_for('topology.view_topology', topology_id=parent_id))
    return redirect(url_for('topology.dashboard'))

@topology_bp.route('/topology/create_sub', methods=['POST'])
@login_required
def create_sub_topology():
    """Alt topoloji oluştur (AJAX)"""
    from flask import jsonify

    data = request.json

    # Gerekli alanları kontrol et
    required_fields = ['parent_icon_id', 'parent_topology_id', 'name']
    if not all(field in data for field in required_fields):
        return jsonify({'success': False, 'error': 'Eksik parametreler.'}), 400

    parent_icon_id = data['parent_icon_id']
    parent_topology_id = data['parent_topology_id']
    name = data['name']
    description = data.get('description', '')

    # Parent topoloji erişim kontrolü
    parent_topology = Topology.query.get_or_404(parent_topology_id)
    if not parent_topology.can_user_edit(current_user.id):
        return jsonify({'success': False, 'error': 'Bu topolojiye düzenleme izniniz yok.'}), 403

    # Parent icon kontrolü
    parent_icon = Icon.query.get_or_404(parent_icon_id)
    if parent_icon.topology_id != parent_topology_id:
        return jsonify({'success': False, 'error': 'Simge belirtilen topolojiye ait değil.'}), 400

    try:
        # Yeni alt topoloji oluştur (description alanı yok, sadece name kullan)
        sub_topology = Topology(
            name=name,
            user_id=current_user.id,
            parent_id=parent_topology_id
        )
        db.session.add(sub_topology)
        db.session.flush()  # ID'yi al

        # Parent icon ile alt topoloji arasında ilişki kur
        parent_icon.sub_topology_id = sub_topology.id

        db.session.commit()

        return jsonify({
            'success': True,
            'topology_id': sub_topology.id,
            'message': f'Alt topoloji "{name}" başarıyla oluşturuldu.'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'Alt topoloji oluşturulurken bir hata oluştu: {str(e)}'}), 500

@topology_bp.route('/topology/<int:topology_id>')
@login_required
def view_topology(topology_id):
    """Topoloji görüntüleme sayfası"""
    topology = Topology.query.get_or_404(topology_id)

    # Erişim kontrolü - Kullanıcı sahibi mi veya paylaşım izni var mı?
    if not topology.can_user_access(current_user.id):
        flash('Bu topolojiye erişim izniniz yok.')
        return redirect(url_for('topology.dashboard'))

    # Kullanıcının izin seviyesini belirle
    user_permission = topology.get_user_permission(current_user.id)
    can_edit = topology.can_user_edit(current_user.id)

    # Topoloji yolunu al
    topology_path = topology.get_path()

    # Alt topolojileri al
    sub_topologies = Topology.query.filter_by(parent_id=topology_id).all()

    # Üst simgeyi al (varsa)
    parent_icon = topology.parent_icon

    return render_template('topology/canvas.html',
                          topology=topology,
                          sub_topologies=sub_topologies,
                          topology_path=topology_path,
                          parent_icon=parent_icon,
                          user_permission=user_permission,
                          can_edit=can_edit)

@topology_bp.route('/notes/<int:topology_id>/<int:icon_id>')
@login_required
def view_notes(topology_id, icon_id):
    """Simge notlarını ayrı bir sayfada görüntüle"""
    topology = Topology.query.get_or_404(topology_id)
    icon = Icon.query.get_or_404(icon_id)

    # Erişim kontrolü
    if not topology.can_user_access(current_user.id):
        flash('Bu topolojiye erişim izniniz yok.')
        return redirect(url_for('topology.dashboard'))

    # Simgenin topolojiye ait olduğunu kontrol et
    if icon.topology_id != topology_id:
        flash('Bu simge belirtilen topolojiye ait değil.')
        return redirect(url_for('topology.view_topology', topology_id=topology_id))

    # Kullanıcının düzenleme izni var mı?
    can_edit = topology.can_user_edit(current_user.id)

    # Simge adını al (URL'den veya veritabanından)
    icon_name = request.args.get('name', icon.name)

    return render_template('topology/notes.html',
                          topology_id=topology_id,
                          topology_name=topology.name,
                          icon_id=icon_id,
                          icon_name=icon_name,
                          can_edit=can_edit)

@topology_bp.route('/topology/<int:topology_id>/manage')
@login_required
def manage_topology_shares(topology_id):
    """Topoloji paylaşım yönetimi sayfası"""
    topology = Topology.query.get_or_404(topology_id)

    # Sadece sahip yönetebilir
    if topology.user_id != current_user.id:
        flash('Bu topolojinin paylaşımlarını yönetme yetkiniz yok.')
        return redirect(url_for('topology.dashboard'))

    return render_template('topology/manage_shares.html', topology=topology)
