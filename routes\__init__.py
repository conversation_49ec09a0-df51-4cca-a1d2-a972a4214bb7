"""
Route modülleri
"""
from flask import Blueprint

def register_routes(app):
    """Tüm route'ları uygulamaya kaydet"""
    
    # Auth routes
    from .auth import auth_bp
    app.register_blueprint(auth_bp)
    
    # Topology routes
    from .topology import topology_bp
    app.register_blueprint(topology_bp)
    
    # API routes
    from .api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Admin routes
    from .admin import admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

__all__ = ['register_routes']
