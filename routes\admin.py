"""
Admin route'ları
"""
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import db, User, Topology, TopologyShare
from utils import delete_topology_recursive, generate_random_password

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/')
@login_required
def dashboard():
    """Admin dashboard"""
    if not current_user.is_admin():
        flash('Bu sayfaya erişim yetkiniz yok.')
        return redirect(url_for('topology.dashboard'))
    
    return render_template('admin/dashboard.html')

@admin_bp.route('/users')
@login_required
def users():
    """Kullanıcı yönetimi sayfası"""
    if not current_user.is_admin():
        flash('Bu sayfaya erişim yetkiniz yok.')
        return redirect(url_for('topology.dashboard'))
    
    return render_template('admin/users.html')

# API Routes

@admin_bp.route('/api/users', methods=['GET'])
@login_required
def get_all_users():
    """Tüm kullanıcıları listele"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403
    
    users = User.query.all()
    users_data = []
    
    for user in users:
        # Kullanıcının topoloji sayısını hesapla
        topology_count = Topology.query.filter_by(user_id=user.id, parent_id=None).count()
        
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active_user,
            'created_at': user.created_at.isoformat(),
            'topology_count': topology_count
        })
    
    return jsonify({'success': True, 'users': users_data})

@admin_bp.route('/api/users/<int:user_id>/toggle-status', methods=['PUT'])
@login_required
def toggle_user_status(user_id):
    """Kullanıcı durumunu aktif/pasif yap"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403
    
    user = User.query.get_or_404(user_id)
    
    # Kendini devre dışı bırakmasını engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi hesabınızı devre dışı bırakamazsınız.'}), 400
    
    try:
        user.is_active_user = not user.is_active_user
        db.session.commit()
        
        status = 'aktif' if user.is_active_user else 'pasif'
        return jsonify({
            'success': True,
            'message': f'Kullanıcı {status} duruma getirildi.',
            'is_active': user.is_active_user
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Durum değiştirme sırasında bir hata oluştu.'}), 500

@admin_bp.route('/api/users/<int:user_id>/reset-password', methods=['PUT'])
@login_required
def reset_user_password(user_id):
    """Kullanıcı şifresini sıfırla"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403
    
    user = User.query.get_or_404(user_id)
    
    try:
        # Yeni şifre oluştur
        new_password = generate_random_password()
        
        user.set_password(new_password)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Şifre başarıyla sıfırlandı.',
            'new_password': new_password
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Şifre sıfırlama sırasında bir hata oluştu.'}), 500

@admin_bp.route('/api/users/<int:user_id>/change-role', methods=['PUT'])
@login_required
def change_user_role(user_id):
    """Kullanıcı rolünü değiştir"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403
    
    user = User.query.get_or_404(user_id)
    data = request.json
    new_role = data.get('role')
    
    if new_role not in ['admin', 'user']:
        return jsonify({'success': False, 'error': 'Geçersiz rol.'}), 400
    
    # Kendinin rolünü değiştirmesini engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi rolünüzü değiştiremezsiniz.'}), 400
    
    try:
        user.role = new_role
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Kullanıcı rolü {new_role} olarak değiştirildi.',
            'new_role': new_role
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Rol değiştirme sırasında bir hata oluştu.'}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    """Kullanıcıyı sil"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': 'Yetkiniz yok.'}), 403
    
    user = User.query.get_or_404(user_id)
    
    # Kendini silmesini engelle
    if user.id == current_user.id:
        return jsonify({'success': False, 'error': 'Kendi hesabınızı silemezsiniz.'}), 400
    
    try:
        # Kullanıcının topolojilerini sil
        topologies = Topology.query.filter_by(user_id=user_id).all()
        for topology in topologies:
            delete_topology_recursive(topology)
        
        # Kullanıcının paylaşımlarını sil
        TopologyShare.query.filter_by(user_id=user_id).delete()
        TopologyShare.query.filter_by(shared_by=user_id).delete()
        
        # Kullanıcıyı sil
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Kullanıcı başarıyla silindi.'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': 'Kullanıcı silinirken bir hata oluştu.'}), 500
