/**
 * Notes Yönetimi JavaScript
 */

// Global değişkenler
let lastSavedContent = '';
let saveTimeout;
let isReadonly = false;

/**
 * Notes editörünü başlat
 */
function initNotesEditor() {
    // Readonly durumunu kontrol et
    isReadonly = document.body.dataset.readonly === 'true';

    // Summernote editörünü başlat
    $('#editor').summernote({
        height: 'calc(100vh - 200px)',
        lang: 'tr-TR',
        fontNames: ['Arial', 'Times New Roman', 'Calibri', 'Georgia', 'Verdana', 'Courier New', 'Tahoma', 'Comic Sans MS', 'Helvetica'],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48', '72'],
        tabsize: 2,
        focus: false,
        dialogsInBody: true,
        disableDragAndDrop: isReadonly,
        toolbar: isReadonly ? false : [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'hr']],
            ['view', ['fullscreen', 'codeview']],
            ['help', ['help']]
        ],
        placeholder: isReadonly ? 'Bu not sadece okunabilir...' : 'Notlarınızı buraya yazın...',
        popover: isReadonly ? {} : {
            air: [
                ['color', ['color']],
                ['font', ['bold', 'underline', 'clear']]
            ]
        },
        callbacks: {
            onInit: function() {
                // Editör yüklendikten sonra notları yükle
                loadNotes();

                // Readonly kullanıcılar için editörü devre dışı bırak
                if (isReadonly) {
                    $('#editor').summernote('disable');
                } else {
                    // Dropdown'ları düzelt (sadece düzenleme modunda)
                    setTimeout(fixDropdowns, 500);
                }
            },
            onChange: function(contents, $editable) {
                if (!isReadonly) {
                    autoSave(contents);
                }
            },
            onKeyup: function(e) {
                updateWordCount();
            }
        },
        disabled: isReadonly
    });
}

/**
 * Dropdown'ları düzelt
 */
function fixDropdowns() {
    $('.note-btn-group .dropdown-toggle').each(function() {
        const $this = $(this);
        const $menu = $this.next('.dropdown-menu');

        if ($menu.length) {
            $this.off('click.dropdown').on('click.dropdown', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Diğer açık dropdown'ları kapat
                $('.dropdown-menu.show').removeClass('show');

                // Bu dropdown'ı aç/kapat
                $menu.toggleClass('show');

                // Pozisyonu ayarla
                const rect = this.getBoundingClientRect();
                $menu.css({
                    position: 'absolute',
                    top: rect.bottom + 'px',
                    left: rect.left + 'px',
                    zIndex: 1050
                });
            });
        }
    });

    // Dışarı tıklandığında dropdown'ları kapat
    $(document).off('click.dropdown').on('click.dropdown', function(e) {
        if (!$(e.target).closest('.note-btn-group').length) {
            $('.dropdown-menu.show').removeClass('show');
        }
    });
}

/**
 * Kelime sayısını güncelle
 */
function updateWordCount() {
    const text = $('#editor').summernote('code').replace(/<[^>]*>/g, '').trim();
    const wordCount = text.length > 0 ? text.split(/\s+/).length : 0;

    // Kelime sayısını göster (eğer element varsa)
    const wordCountElement = document.getElementById('word-count');
    if (wordCountElement) {
        wordCountElement.textContent = `${wordCount} kelime`;
    }
}

/**
 * Notları yükle
 */
function loadNotes() {
    const iconId = document.body.dataset.iconId;

    fetch(`/api/notes/${iconId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notes && data.notes.length > 0) {
                // İlk notu al (en son oluşturulan)
                const note = data.notes[0];
                $('#editor').summernote('code', note.content || '');
                lastSavedContent = note.content || '';
            } else {
                $('#editor').summernote('code', '');
                lastSavedContent = '';
            }

            // Kelime sayısını güncelle
            updateWordCount();
        })
        .catch(error => {
            console.error('Notlar yüklenirken hata:', error);
            showAlert('danger', 'Notlar yüklenirken bir hata oluştu.');
        });
}

/**
 * Otomatik kaydetme
 */
function autoSave(content) {
    // Önceki timeout'u iptal et
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // İçerik değişmemişse kaydetme
    if (content === lastSavedContent) {
        return;
    }

    // 2 saniye sonra kaydet
    saveTimeout = setTimeout(() => {
        saveNote(content);
    }, 2000);
}

/**
 * Notu kaydet
 */
function saveNote(content) {
    const iconId = document.body.dataset.iconId;

    // Kaydetme göstergesi
    showSaveIndicator('Kaydediliyor...');

    fetch(`/api/notes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            icon_id: parseInt(iconId),
            content: content
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            lastSavedContent = content;
            showSaveIndicator('Kaydedildi', 'success');
        } else {
            showSaveIndicator('Kaydetme hatası', 'error');
            console.error('Not kaydetme hatası:', data.error);
        }
    })
    .catch(error => {
        showSaveIndicator('Kaydetme hatası', 'error');
        console.error('Not kaydetme hatası:', error);
    });
}

/**
 * Manuel kaydetme
 */
function manualSave() {
    if (isReadonly) {
        showAlert('warning', 'Bu topolojide sadece okuma izniniz var.');
        return;
    }

    const content = $('#editor').summernote('code');

    // Timeout'u iptal et
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    saveNote(content);
}

/**
 * Kaydetme göstergesi göster
 */
function showSaveIndicator(message, type = 'info') {
    const indicator = document.getElementById('saveIndicator');

    if (indicator) {
        indicator.textContent = message;
        indicator.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
        indicator.style.display = 'block';

        // 3 saniye sonra gizle
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 3000);
    }
}

/**
 * Alert göster
 */
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Sayfanın üstüne ekle
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }

    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Geri dön
 */
function goBack() {
    const topologyId = document.body.dataset.topologyId;
    window.location.href = `/topology/${topologyId}`;
}

/**
 * Yazdır
 */
function printNotes() {
    const content = $('#editor').summernote('code');
    const iconName = document.body.dataset.iconName;
    const topologyName = document.body.dataset.topologyName;

    // Yeni pencere aç
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${iconName} - Notlar</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { line-height: 1.6; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <h1>${iconName} - Notlar</h1>
            <div class="meta">
                <strong>Topoloji:</strong> ${topologyName}<br>
                <strong>Yazdırma Tarihi:</strong> ${new Date().toLocaleDateString('tr-TR')}
            </div>
            <div class="content">
                ${content}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Yazdırma dialog'unu aç
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

// Sayfa yüklendiğinde editörü başlat
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('editor')) {
        initNotesEditor();
    }
});

// Sayfa kapatılırken kaydetmemiş değişiklikleri kontrol et
window.addEventListener('beforeunload', function(e) {
    if (!isReadonly) {
        const content = $('#editor').summernote('code');
        if (content !== lastSavedContent) {
            e.preventDefault();
            e.returnValue = 'Kaydedilmemiş değişiklikleriniz var. Sayfayı kapatmak istediğinizden emin misiniz?';
        }
    }
});
