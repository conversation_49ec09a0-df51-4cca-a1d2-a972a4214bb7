{% extends "base/layout.html" %}

{% block title %}Kullanıcı Yönetimi - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-people"></i> Kullanıcı Yönetimi
                </h1>
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Admin Dashboard'a Dön
                </a>
            </div>
        </div>
    </div>

    <!-- Kullanı<PERSON>ı Listesi -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list"></i> <PERSON><PERSON><PERSON><PERSON><PERSON> Listesi
                    </h5>
                </div>
                <div class="card-body">
                    <div id="usersTable">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Yükleniyor...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Şifre Sıfırlama Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">Şifre Sıfırla</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bu kullanıcının şifresini sıfırlamak istediğinizden emin misiniz?</p>
                <p><strong id="resetPasswordUserInfo"></strong></p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    Yeni şifre otomatik olarak oluşturulacak ve size gösterilecektir.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-warning" id="confirmResetPasswordBtn">Şifreyi Sıfırla</button>
            </div>
        </div>
    </div>
</div>

<!-- Yeni Şifre Gösterme Modal -->
<div class="modal fade" id="newPasswordModal" tabindex="-1" aria-labelledby="newPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newPasswordModalLabel">Yeni Şifre</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i>
                    Şifre başarıyla sıfırlandı!
                </div>
                <p>Kullanıcının yeni şifresi:</p>
                <div class="input-group">
                    <input type="text" class="form-control" id="newPasswordDisplay" readonly>
                    <button class="btn btn-outline-secondary" type="button" onclick="copyPassword()">
                        <i class="bi bi-clipboard"></i> Kopyala
                    </button>
                </div>
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Önemli:</strong> Bu şifreyi kullanıcıya güvenli bir şekilde iletin ve bu pencereyi kapattıktan sonra şifre bir daha gösterilmeyecektir.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Tamam</button>
            </div>
        </div>
    </div>
</div>

<!-- Kullanıcı Silme Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">Kullanıcı Sil</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Dikkat!</strong> Bu işlem geri alınamaz.
                </div>
                <p>Bu kullanıcıyı silmek istediğinizden emin misiniz?</p>
                <p><strong id="deleteUserInfo"></strong></p>
                <p class="text-muted">
                    <small>
                        • Kullanıcının tüm topolojileri silinecek<br>
                        • Tüm paylaşımları kaldırılacak<br>
                        • Bu işlem geri alınamaz
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteUserBtn">Kullanıcıyı Sil</button>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/admin.js') }}"></script>
<script>
let currentUserId = null;

// Sayfa yüklendiğinde kullanıcıları getir
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
});

function loadUsers() {
    fetch('/admin/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.users);
            } else {
                document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            }
        })
        .catch(error => {
            console.error('Kullanıcılar yüklenirken hata:', error);
            document.getElementById('usersTable').innerHTML = '<div class="alert alert-danger">Kullanıcılar yüklenirken bir hata oluştu.</div>';
        });
}

function displayUsers(users) {
    const tableDiv = document.getElementById('usersTable');

    if (users.length === 0) {
        tableDiv.innerHTML = '<div class="alert alert-info">Henüz kullanıcı bulunmuyor.</div>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
    html += '<thead class="table-dark"><tr>';
    html += '<th>ID</th><th>Kullanıcı Adı</th><th>Email</th><th>Rol</th><th>Durum</th><th>Topoloji</th><th>Kayıt Tarihi</th><th>İşlemler</th>';
    html += '</tr></thead><tbody>';

    users.forEach(user => {
        const statusBadge = user.is_active
            ? '<span class="badge bg-success">Aktif</span>'
            : '<span class="badge bg-danger">Pasif</span>';

        const roleBadge = user.role === 'admin'
            ? '<span class="badge bg-primary">Admin</span>'
            : '<span class="badge bg-secondary">Kullanıcı</span>';

        const registerDate = new Date(user.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });

        html += `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${roleBadge}</td>
                <td>${statusBadge}</td>
                <td>${user.topology_count}</td>
                <td>${registerDate}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-warning" onclick="resetPassword(${user.id}, '${user.username}')" title="Şifre Sıfırla">
                            <i class="bi bi-key"></i>
                        </button>
                        <button type="button" class="btn btn-outline-${user.is_active ? 'secondary' : 'success'}" onclick="toggleUserStatus(${user.id}, '${user.username}', ${user.is_active})" title="${user.is_active ? 'Pasif Yap' : 'Aktif Yap'}">
                            <i class="bi bi-${user.is_active ? 'pause' : 'play'}"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="changeRole(${user.id}, '${user.username}', '${user.role}')" title="Rol Değiştir">
                            <i class="bi bi-shield"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.username}')" title="Kullanıcı Sil">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    tableDiv.innerHTML = html;
}

// Kullanıcı durumunu değiştir
function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? 'pasif' : 'aktif';

    if (!confirm(`${username} kullanıcısını ${action} yapmak istediğinizden emin misiniz?`)) {
        return;
    }

    fetch(`/admin/api/users/${userId}/toggle-status`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUsers();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Durum değiştirme hatası:', error);
        showAlert('danger', 'Durum değiştirme sırasında bir hata oluştu.');
    });
}

// Şifre sıfırlama
function resetPassword(userId, username) {
    currentUserId = userId;
    document.getElementById('resetPasswordUserInfo').textContent = username;
    new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
}

// Şifre sıfırlama onayı
document.getElementById('confirmResetPasswordBtn').addEventListener('click', function() {
    fetch(`/admin/api/users/${currentUserId}/reset-password`, {
        method: 'PUT'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Şifre sıfırlama modal'ını kapat
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();

            // Yeni şifreyi göster
            document.getElementById('newPasswordDisplay').value = data.new_password;
            new bootstrap.Modal(document.getElementById('newPasswordModal')).show();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Şifre sıfırlama hatası:', error);
        showAlert('danger', 'Şifre sıfırlama sırasında bir hata oluştu.');
    });
});

// Rol değiştirme
function changeRole(userId, username, currentRole) {
    const newRole = currentRole === 'admin' ? 'user' : 'admin';

    if (!confirm(`${username} kullanıcısının rolünü ${newRole} yapmak istediğinizden emin misiniz?`)) {
        return;
    }

    fetch(`/admin/api/users/${userId}/change-role`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            role: newRole
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUsers();
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        console.error('Rol değiştirme hatası:', error);
        showAlert('danger', 'Rol değiştirme sırasında bir hata oluştu.');
    });
}

// Kullanıcı silme
function deleteUser(userId, username) {
    currentUserId = userId;
    document.getElementById('deleteUserInfo').textContent = username;
    new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
}

// Kullanıcı silme onayı
document.getElementById('confirmDeleteUserBtn').addEventListener('click', function() {
    fetch(`/admin/api/users/${currentUserId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUsers();
        } else {
            showAlert('danger', data.error);
        }

        // Modal'ı kapat
        bootstrap.Modal.getInstance(document.getElementById('deleteUserModal')).hide();
    })
    .catch(error => {
        console.error('Kullanıcı silme hatası:', error);
        showAlert('danger', 'Kullanıcı silme sırasında bir hata oluştu.');
    });
});

// Şifre kopyalama
function copyPassword() {
    const passwordField = document.getElementById('newPasswordDisplay');
    passwordField.select();
    passwordField.setSelectionRange(0, 99999);
    navigator.clipboard.writeText(passwordField.value);

    // Kopyalama feedback'i
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-check"></i> Kopyalandı';
    setTimeout(() => {
        button.innerHTML = originalText;
    }, 2000);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Sayfanın üstüne ekle
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);

    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
