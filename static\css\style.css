/* Ana stil dosyası - Flat, Minimalist ve <PERSON><PERSON><PERSON>m - Kompakt */
:root {
    /* <PERSON><PERSON><PERSON> renk paleti */
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --light-bg: #f7f9fa;
    --dark-bg: #2c3e50;
    --text-color: #333333;
    --text-light: #7f8c8d;
    --border-color: #e0e0e0;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;

    /* Font boyutları */
    --font-size-xs: 0.7rem;
    --font-size-sm: 0.75rem;
    --font-size-md: 0.8rem;
    --font-size-lg: 0.9rem;
    --font-size-xl: 1rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
}

body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    color: var(--text-color);
    font-size: var(--font-size-md);
    line-height: 1.4;
}

.navbar {
    box-shadow: none;
    margin-bottom: 0;
    background-color: var(--dark-bg) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xs) var(--spacing-md);
    min-height: 40px;
}

.navbar-brand {
    font-size: var(--font-size-lg);
    padding: var(--spacing-xs) 0;
}

.nav-link {
    font-size: var(--font-size-md);
    padding: var(--spacing-xs) var(--spacing-sm) !important;
}

.card {
    box-shadow: none;
    border: 1px solid var(--border-color);
    margin-bottom: 0;
    border-radius: 0;
}

.card-header {
    padding: var(--spacing-sm) var(--spacing-md);
}

.btn {
    border-radius: 2px;
    text-transform: uppercase;
    font-size: var(--font-size-xs);
    font-weight: 500;
    letter-spacing: 0.5px;
    padding: var(--spacing-xs) var(--spacing-md);
    transition: all 0.2s ease;
    line-height: 1.2;
}

.navigation-container {
    background-color: var(--light-bg);
    box-shadow: none;
    margin-bottom: 0;
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-md);
}

/* Canvas stilleri */
/* Canvas ve Zoom/Pan stilleri - N8N tarzında - Flat ve Minimalist - Kompakt */
#canvas-container {
    border-radius: 0;
    height: calc(100vh - 120px);
    position: relative;
    overflow: hidden;
    width: 100%;
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
}

/* Canvas katmanları */
.canvas-layers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-origin: 0 0;
    will-change: transform;
}

#topology-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 8000px; /* Daha geniş bir çalışma alanı */
    height: 8000px;
    transform-origin: 0 0;
}

/* Arka plan - düz beyaz */
.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 8000px;
    height: 8000px;
    background-color: white;
    pointer-events: none;
    z-index: 0;
}

/* Zoom kontrolleri */
.zoom-controls {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    border: 1px solid var(--border-color);
    z-index: 100;
}

.zoom-button {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: none;
    font-size: var(--font-size-md);
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--text-color);
}

.zoom-button:hover {
    background-color: var(--light-bg);
    color: var(--accent-color);
}

.zoom-button:first-child {
    border-radius: 0;
    border-bottom: 1px solid var(--border-color);
}

.zoom-button:last-child {
    border-radius: 0;
}

.zoom-level {
    padding: 3px 0;
    text-align: center;
    font-size: var(--font-size-xs);
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
}

/* Mini harita */
.mini-map {
    position: absolute;
    bottom: 12px;
    left: 12px;
    width: 160px;
    height: 120px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    z-index: 100;
    display: none;
}

.mini-map-content {
    position: relative;
    width: 100%;
    height: 100%;
    transform-origin: 0 0;
}

.mini-map-viewport {
    position: absolute;
    border: 1px solid var(--accent-color);
    background-color: rgba(52, 152, 219, 0.1);
    cursor: move;
}

/* Sağ tıklama menüsü */
.context-menu {
    position: fixed; /* fixed pozisyonlama kullan */
    background-color: white;
    border-radius: 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    z-index: 10000; /* Sağ tık menüsünün z-index değerini daha da artır */
    min-width: 160px;
    padding: 0;
    font-size: var(--font-size-sm);
    pointer-events: auto; /* Tıklama olaylarını etkinleştir */
    border: 1px solid var(--border-color);
}

.context-menu-item {
    padding: var(--spacing-xs) var(--spacing-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
    color: var(--text-color);
}

.context-menu-item:hover {
    background-color: var(--light-bg);
    color: var(--accent-color);
}

.context-menu-item i {
    margin-right: 8px;
    font-size: var(--font-size-sm);
    width: 16px;
    text-align: center;
}

.context-menu-separator {
    height: 1px;
    background-color: var(--border-color);
    margin: 0;
}

/* Bağlantı stilleri */
.connection-path {
    position: absolute;
    fill: none;
    stroke: var(--text-light);
    stroke-width: 1.5px;
    z-index: 55;
    pointer-events: stroke;
    transition: stroke 0.2s, stroke-width 0.2s;
    opacity: 1 !important;
    visibility: visible !important;
}

.connection-path.selected {
    stroke: var(--accent-color);
    stroke-width: 2px;
    filter: none;
}

.connection-path:hover {
    stroke: var(--accent-color);
    stroke-width: 2px;
    filter: none;
}

.connection-arrow {
    fill: var(--text-light);
    stroke: none;
    transition: fill 0.2s;
    opacity: 1 !important;
    visibility: visible !important;
}

.connection-arrow.selected {
    fill: var(--accent-color);
    filter: none;
}

/* Geçici bağlantı stilleri */
.temp-connection-path {
    fill: none;
    stroke: var(--accent-color);
    stroke-width: 1.5px;
    stroke-dasharray: 4, 2;
    animation: dash 1s linear infinite;
    z-index: 95;
}

.temp-connection-arrow {
    fill: var(--accent-color);
}

@keyframes dash {
    to {
        stroke-dashoffset: -6;
    }
}

/* Bağlantı noktaları (portlar) */
.port {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid var(--text-light);
    z-index: 100; /* Port z-index değerini artır */
    cursor: pointer;
    transition: all 0.2s;
    pointer-events: auto; /* Tıklama olaylarını etkinleştir */
}

.port:hover, .port.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    transform: scale(1.2);
    box-shadow: none;
}

.port.highlight {
    background-color: var(--success-color);
    border-color: var(--success-color);
    transform: scale(1.2);
    box-shadow: none;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.5);
    }
    70% {
        box-shadow: 0 0 0 3px rgba(46, 204, 113, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
    }
}

.port.input {
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: none;
}

.port.output {
    right: -5px;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: none;
}

.port.input:hover, .port.input.active {
    transform: translateY(-50%) scale(1.2);
}

.port.output:hover, .port.output.active {
    transform: translateY(-50%) scale(1.2);
}

/* Toast container */
.toast-container {
    z-index: 9999;
}

.connection-label {
    position: absolute;
    background-color: white;
    padding: 2px 4px;
    border-radius: 0;
    font-size: var(--font-size-xs);
    z-index: 56;
    cursor: pointer;
    border: 1px solid var(--border-color);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-shadow: none;
    color: var(--text-color);
}

.connection-label:hover {
    background-color: var(--light-bg);
    border-color: var(--accent-color);
}

.connection-label.selected {
    background-color: var(--light-bg);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.connection-mode-active #topology-canvas {
    cursor: crosshair;
}

.connection-mode-active .icon:hover {
    border: 1px dashed var(--accent-color);
}

/* Simge stilleri */
.icon {
    transition: all 0.2s ease;
    border-radius: 0;
    padding: var(--spacing-xs);
    position: absolute;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 70px;
    z-index: 50; /* Simgelerin z-index değerini artır */
}

.icon:hover {
    transform: scale(1.02);
    background-color: rgba(0, 0, 0, 0.02);
    z-index: 10;
}

.icon-image {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--accent-color);
    width: 100px;
    height: 100px;
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    border: 1px solid var(--border-color);
}

.icon-name {
    text-align: center;
    font-size: var(--font-size-xs);
    background-color: white;
    padding: 20px 40px;
    border-radius: 0;
    margin-top: 4px;
}

.icon-name-edit {
    width: 100%;
    border: 1px solid var(--accent-color);
    border-radius: 0;
    padding: 2px 4px;
    font-size: var(--font-size-xs);
    text-align: center;
}

.icon.selected {
    background-color: rgba(52, 152, 219, 0.05);
    border: 1px solid var(--accent-color);
    box-shadow: none;
}

.icon.connection-source {
    background-color: rgba(46, 204, 113, 0.05);
    border: 1px solid var(--success-color);
    box-shadow: none;
}

.icon.selected .icon-name {
    color: var(--accent-color);
    font-weight: 500;
    background-color: white;
    border-radius: 0;
    padding: 2px 4px;
    border: 1px solid var(--accent-color);
}

/* Not paneli stilleri */
#notes-panel {
    padding: var(--spacing-sm);
    height: calc(100vh - 300px);
    overflow-y: auto;
}

.note-card {
    margin-bottom: var(--spacing-sm);
    border-left: 2px solid var(--accent-color);
}

/* OneNote tarzı editör stilleri */
.onenote-editor {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    border-radius: 0;
    overflow: hidden;
    height: calc(100vh - 350px);
}

.editor-toolbar {
    display: flex;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    align-items: center;
}

.editor-toolbar button {
    margin-right: var(--spacing-xs);
    border-radius: 0;
    padding: 2px 6px;
}

.editor-status {
    margin-left: auto;
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.note-content {
    flex: 1;
    padding: var(--spacing-sm);
    overflow-y: auto;
    background-color: white;
    min-height: 180px;
    outline: none;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.note-content:focus {
    box-shadow: none;
    border-left: 2px solid var(--accent-color);
}

/* Yeniden boyutlandırma stilleri */
.main-canvas-area {
    transition: width 0.2s ease;
    min-width: 200px;
    height: calc(100vh - 120px);
}

.notes-area {
    transition: width 0.2s ease;
    min-width: 150px;
    max-width: 50%;
    height: calc(100vh - 120px);
    overflow-y: auto;
    border-left: 1px solid var(--border-color);
}

.resizer {
    width: 4px;
    background-color: var(--light-bg);
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.resizer-handle {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.resizer:hover {
    background-color: var(--border-color);
}

.resizer:hover .resizer-handle {
    color: var(--text-color);
}

.notes-area.collapsed {
    width: 0 !important;
    padding: 0;
    margin: 0;
    overflow: hidden;
    opacity: 0;
}

.main-canvas-area.expanded {
    flex: 1 !important;
}

.resizer.active {
    background-color: var(--accent-color);
}

.row.g-0 {
    display: flex;
    width: 100%;
    height: calc(100vh - 120px);
    overflow: hidden;
}

/* Icon palette styles */
.icon-palette {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: none;
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    z-index: 1000;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
}

.icon-palette-item {
    width: 48px;
    height: 48px;
    margin: 3px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.icon-palette-item:hover {
    background-color: var(--light-bg);
    transform: translateY(-2px);
    border: 1px solid var(--border-color);
}

.icon-palette-item i {
    font-size: var(--font-size-lg);
    margin-bottom: 3px;
    color: var(--accent-color);
}

.icon-palette-item span {
    font-size: var(--font-size-xs);
    text-align: center;
    color: var(--text-color);
}

/* Modal styles */
.modal-content {
    border-radius: 0;
    border: 1px solid var(--border-color);
}

.modal-header {
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-md);
}

.modal-title {
    font-size: var(--font-size-md);
    font-weight: 500;
}

.modal-body {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.modal-footer {
    background-color: var(--light-bg);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-md);
}

/* Form elements */
.form-control {
    border-radius: 0;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
}

.form-label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.form-text {
    font-size: var(--font-size-xs);
}

/* Alert styles */
.alert {
    border-radius: 0;
    border: none;
    border-left: 2px solid;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--text-color);
    border-left-color: var(--info-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.05);
    color: var(--text-color);
    border-left-color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.05);
    color: var(--text-color);
    border-left-color: var(--danger-color);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.05);
    color: var(--text-color);
    border-left-color: var(--success-color);
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
    .col-md-9, .col-md-3 {
        margin-bottom: var(--spacing-sm);
    }

    #canvas-container {
        height: 400px;
    }

    #notes-panel {
        height: 300px;
    }
}
