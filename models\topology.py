"""
Topoloji modelleri
"""
from datetime import datetime
from . import db

class Topology(db.Model):
    """Topoloji modeli"""

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    parent_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('topology.id'), nullable=True)
    parent = db.relationship('Topology', remote_side=[id], backref='children', uselist=False)
    background_image = db.Column(db.String(255), nullable=True)  # Arkaplan resmi URL'si
    background_x = db.Column(db.Float, default=0)  # Arkaplan resmi X konumu
    background_y = db.Column(db.Float, default=0)  # Arkaplan resmi Y konumu
    background_scale = db.Column(db.Float, default=1.0)  # Arkaplan resmi ölçeği
    current_version = db.Column(db.Integer, default=1)  # Mevcut versiyon numarası

    # İlişkiler
    versions = db.relationship('TopologyVersion', backref='topology', lazy='dynamic', cascade='all, delete-orphan')
    shares = db.relationship('TopologyShare', backref='topology', lazy=True, cascade='all, delete-orphan')

    def get_path(self):
        """Topolojinin tam yolunu döndürür (Ana Topoloji > Alt Topoloji > ...)"""
        from .connection import Icon  # Import burada yapıyoruz

        path = []
        current = self

        while current:
            # Alt topoloji ise ve parent icon varsa, icon adını kullan
            if current.parent_id:
                # Parent icon'ı manuel olarak bul
                parent_icon = Icon.query.filter_by(sub_topology_id=current.id).first()
                if parent_icon:
                    current.display_name = parent_icon.name
                else:
                    current.display_name = current.name
            else:
                # Ana topoloji ise normal adını kullan
                current.display_name = current.name

            path.insert(0, current)
            current = current.parent

        return path

    def get_user_permission(self, user_id):
        """Belirli bir kullanıcının bu topoloji için iznini döndürür"""
        if self.user_id == user_id:
            return 'owner'

        share = TopologyShare.query.filter_by(topology_id=self.id, user_id=user_id).first()
        if share:
            return share.permission

        return None

    def can_user_access(self, user_id):
        """Kullanıcının bu topolojiye erişip erişemeyeceğini kontrol eder"""
        return self.get_user_permission(user_id) is not None

    def can_user_edit(self, user_id):
        """Kullanıcının bu topolojiyi düzenleyip düzenleyemeyeceğini kontrol eder"""
        permission = self.get_user_permission(user_id)
        return permission in ['owner', 'edit']

    def __repr__(self):
        return f'<Topology {self.name}>'

class TopologyShare(db.Model):
    """Topoloji paylaşım modeli"""

    id = db.Column(db.Integer, primary_key=True)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission = db.Column(db.String(20), nullable=False)  # 'readonly' veya 'edit'
    shared_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # İlişkiler
    shared_by_user = db.relationship('User', foreign_keys=[shared_by], backref='shared_topologies_by_me')

    # Unique constraint - Aynı kullanıcıya aynı topoloji birden fazla kez paylaşılamaz
    __table_args__ = (db.UniqueConstraint('topology_id', 'user_id', name='unique_topology_user_share'),)

    def __repr__(self):
        return f'<TopologyShare {self.topology_id}-{self.user_id}>'

class TopologyVersion(db.Model):
    """Topoloji versiyon modeli"""

    id = db.Column(db.Integer, primary_key=True)
    topology_id = db.Column(db.Integer, db.ForeignKey('topology.id'), nullable=False)
    version_number = db.Column(db.Integer, nullable=False)
    description = db.Column(db.String(255), nullable=True)
    data = db.Column(db.Text, nullable=False)  # JSON formatında topoloji verisi
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, topology_id, version_number, data, description=None):
        self.topology_id = topology_id
        self.version_number = version_number
        self.data = data
        self.description = description

    def __repr__(self):
        return f'<TopologyVersion {self.topology_id}-v{self.version_number}>'
